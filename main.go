package main

import (
	"context"
	"flag"
	"fmt"
	"testing"

	copywriter_go_SDK "git.xiaojukeji.com/falcon/copywriter-go-SDK"
	"git.xiaojukeji.com/falcon/fission-sdk/go/src/fissionclient"
	bizlog "git.xiaojukeji.com/falcon/go-biz/components/log"
	"git.xiaojukeji.com/falcon/go-biz/components/zconf"
	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/conf"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/ddmq"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/dirpc"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/mysql"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/redis"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/signpost"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/server"
	"git.xiaojukeji.com/falcon/zarya-async-api/models/rpc/ceres"
	"git.xiaojukeji.com/falcon/zarya-async-api/models/rpc/duse"
	"git.xiaojukeji.com/falcon/zarya-async-api/models/rpc/route_broker"
	"git.xiaojukeji.com/falcon/zarya-async-api/models/rpc/unique"
	"git.xiaojukeji.com/nuwa/go-monitor"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	"go.intra.xiaojukeji.com/platform-ha/onekey-degrade_sdk_go/degrade"
)

var (
	confPath string
)

// 建议子模块的初始化，都使用大写的Init函数进行显示初始化，使用golang的init将会导致无序加载，并且无法进行bug排查追踪。
// 只有全局所需的变量、模块、功能，才建议放置到main.go中进行初始化，否则进行懒加载模式，使用时再进行初始化。
func init() {
	testing.Init()
	//设置配置文件的路径，不同机房的配置文件可能不同，这是在control.sh里进行机房判断识别的
	flag.StringVar(&confPath, "c", "./conf", "-c set config file path")
	flag.Parse()
	fmt.Printf("confPath is %s\n", confPath)

	//初始化配置文件,解析app.toml，读取redis、mysql、fusion等资源配置
	//跨机房的不同配置，建议放置到app.toml，其他配置比如caller、token，可以放置到consts文件中
	conf.InitConf()

	//初始化logger，后续步骤的错误信息，可能会输出到logger内部
	log.Init()

	//初始化dirpcSDK，以及disf服务发现。配置文件位于app.toml文件同级的dirpc.json，disf.yaml
	//注意，如果要修改某一个disf下游的连接、读取、写入时间，可以在disf平台操作
	dirpc.Init(confPath)

	//初始化apollo
	if err := apollo.AutoInit(); err != nil {
		panic("failed to init apollo" + err.Error())
	}

	//初始化mysql连接，注意mysql一般都有白名单，如果没有在odin中添加白名单，会导致panic
	//白名单参考：http://odin.xiaojukeji.com/#/tree/machine/operate?category=cluster //&isLeaf=true&ns=us01-v.zarya-passenger-deal.zarya.falcon.ibt.didi.com&region=us01
	mysql.Init()
	ddmq.Init()

	//初始化redis连接,注意redis也有白名单，参考mysql初始化
	redis.Init()
	route_broker.Init()
	duse.InitClient()
	ceres.Init()
	signpost.Init()
	unique.Init()

	//初始化zarya框架的错误码映射，用于后续在组件内部进行错误处理。 比如：component.ErrorResponse(Error.ParamError)
	zerr.Init(consts.ErrMsgMap)

	//TODO 修改为真正的模块，比如passenger/hermes/driver
	zconf.GetUpperAppConfig().SetModuleDriver()

	//初始化i18n sdk，sim环境如果需要强制拉取文案，使用 curl -s http://10.90.28.42:8052/static/anything/getCopywriter.sh  | sh -s
	err := copywriter_go_SDK.AutoInit()
	if err != nil {
		panic("failed to initialize copywriter" + err.Error())
	}

	err = fissionclient.AutoInit()
	if err != nil {
		fmt.Println("fission SDK AutoInit got error:", err)
		panic(err)
	} else {
		fmt.Println("fission SDK AutoInit successfully.")
	}

	//zarya框架所需的初始化，注入logger
	bizlog.Init(log.Trace, nil)

	//初始化降级sdk,后续才能使用,文档请参考：https://cooper.didichuxing.com/knowledge/2199512918173/2199608849136
	//后续降级请使用 git.xiaojukeji.com/falcon/go-biz/component/degrade.go,降级方法
	//
	//  //带有参数的降级方式，主要传递country_code,city_id,会找对应国家城市的降级
	//  degrade.IsDegradeByParam("降级key",map[string]interface{ "city_id" : 2000})
	//
	//  //不带有参数的降级方式，主要用于整体降级所有的功能
	//  degrade.IsDegradeByParam("降级key")
	//
	// 替换为zarya通用的路径，减少资源消耗
	err = degrade.Setup("/home/<USER>/zarya-project", degrade.NewConfigMeta())
	if err != nil {
		panic("failed to setup degrade sdk" + err.Error())
	}
}

func main() {
	// 服务监控，打通odin，参考：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=125476958
	// nolint:errcheck
	go monitor.Start(":9981", monitor.AllPlugin)

	// 启动服务
	if err := server.Run(); err != nil {
		log.Fatalf(context.Background(), consts.ErrnoInnerError, "server Run err %v \n", err)
		return
	}
}
