package consts

const (
	// PressureHintCode is hintcode of Pressure
	PressureHintCode int64 = 1

	// parseMultiFrom时限制的内存使用大小偏移量
	BitMove                  = 20
	CallerPassenger          = "passenger"
	CommonCaller             = "driver"     //订单系统caller,kronos
	DriverProductId          = 2            //请求司机系统传的productId,和订单的productId无关
	DriverCaller             = "driver-api" //司机系统请求的calle
	AppTypePassenger         = 1016
	AppTypeDriver            = 1017
	AppTypeDomesticPassenger = 5009

	IceDefaultProduct = "gs" //ice服务生成orderid用到的product常量
)

const (
	DriverBookingOrderFeature = "wl_dr_bk"
)

const (
	// DeliverStatusSyncLock 运单状态同步锁
	DeliverStatusSyncLock = "ibt_driver_deliver_status_sync_lock_%s"
	// DeliverStatusSyncLockExpireTime 运单状态同步锁默认过期时间 1s
	DeliverStatusSyncLockExpireTime = 1

	// DeliveryToOrderCacheKey 运单状态订单状态映射
	DeliveryToOrderCacheKey = "ibt_driver_deliver_to_order_%s"
	// DeliveryToOrderCacheExpireTime 运单信息缓存过期时间 1day
	DeliveryToOrderCacheExpireTime = 24 * 60 * 60

	DeliveryUnassignStatus     = 110 //未分配（创建订单、改派）
	DeliveryStriveStatus       = 120 //120-0: 引擎分单,但未成单; 120-1、121-2: 成单
	DeliveryArrivedStartStatus = 130 //到店
	DeliveryTakeStatus         = 140 //已取货(开始送货-BeginCharge)
	DeliveryArrivedDestStatus  = 150 //到目的地
	DeliveryFinishStatus       = 160 //完单
	DeliveryCancelStatus       = 170 //取消
	DeliveryAbortStatus        = 190 //关单
	DeliveryRejectStatus       = 182 //司机拒单
	DeliveryReassignStatus     = 180 //改派
	DeliveryRescheduleStatus   = 181 //重调度
	DeliverySendBackStatus     = 255 //退货

	PointTypeStartingPoint = 0
	PointTypeEndPoint      = 3
	PointTypeWayPoint      = 4
)
