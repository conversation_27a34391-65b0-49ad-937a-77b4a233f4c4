package httpserv

import (
	"context"

	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/conf"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/controller"
	"git.xiaojukeji.com/falcon/zarya-async-api/controller/booking_order"
	httpTrace "git.xiaojukeji.com/falcon/zarya-async-api/middleware/http-trace"

	"git.xiaojukeji.com/nuwa/golibs/httpserver/middleware"
	"git.xiaojukeji.com/nuwa/golibs/rpcserver/v2/rpcserver"
	"github.com/grpc-ecosystem/grpc-gateway/runtime"
)

var (
	svr = rpcserver.New()

	httpRegister = func(ctx context.Context, mux *runtime.ServeMux) error {
		return nil
	}
)

// Run 启动服务
func Run() error {
	/* 读取conf配置 */
	svr.SetHTTPAddr(conf.Viper.GetString("rpc.http_addr"))
	svr.SetHTTPReadTimeout(conf.Viper.GetInt("rpc.http_read_timeout"))
	svr.SetHTTPIdleTimeout(conf.Viper.GetInt("rpc.http_idle_timeout"))

	/* 服务注册 */
	svr.SetHTTPRegister(httpRegister)

	/* http 中间件，可以自定义添加， 更多http中间件参考：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=132079586 */
	// recovery中间件
	svr.AddHTTPMiddleware(middleware.RecoveryWithConfig(middleware.RecoveryConfig{Log: log.Trace}))

	// trace中间件
	svr.AddHTTPMiddleware(httpTrace.TraceWithConfig(httpTrace.TraceConfig{Log: log.Trace}))

	// metric 上报中间件
	svr.AddHTTPMiddleware(middleware.MetricAccess(middleware.MetricConfig{Log: log.Trace}))

	// 设置默认编解码方式
	svr.SetDefaultMarshaler()

	runtime.HTTPError = DefaultHTTPError

	/* 添加http handle，如上传文件，页面等等 */
	// svr.AddHTTPHandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
	//     w.Write([]byte("test"))
	// })

	return svr.HttpRun()
}

func RegisterRoutes() {
	svr.AddHTTPHandleFunc("/zarya/async/api/booking-order-auto-pickup-dispatcher", booking_order.BookingOrderAutoPickupDispatcher)
	svr.AddHTTPHandleFunc("/zarya/async/api/booking-order-auto-pickup-operator", booking_order.BookingOrderAutoPickupOperator)
	svr.AddHTTPHandleFunc("/zarya/async/api/booking-order-change", booking_order.BookingOrderChange)
	svr.AddHTTPHandleFunc("/zarya/async/api/booking-order-duse-recover", booking_order.BookingOrderDuseRecover)
	svr.AddHTTPHandleFunc("/zarya/async/api/booking-order-schedule-remind-dispatcher", booking_order.BookingOrderScheduleRemindDispatcher)
	svr.AddHTTPHandleFunc("/zarya/async/api/booking-order-schedule-remind-operator", booking_order.BookingOrderScheduleRemindOperator)

	svr.AddHTTPHandleFunc("/zarya/async/api/delivery_status_sync", controller.DeliveryStatusSync)
	svr.AddHTTPHandleFunc("/zarya/async/api/delivery-status-sync", controller.DeliveryStatusSync)
	svr.AddHTTPHandleFunc("/zarya/async/api/wanliu-driver-pickup-hook", controller.WanliuDriverPickupHook)
	svr.AddHTTPHandleFunc("/zarya/async/api/wanliu-order-arrived-hook", controller.WanliuOrderArrivedHook)
	svr.AddHTTPHandleFunc("/zarya/async/api/wanliu-order-strived-hook", controller.WanliuOrderStrivedHook)
	svr.AddHTTPHandleFunc("/zarya/async/api/wanliu-order-new-hook", controller.WanliuOrderNewHook)
	svr.AddHTTPHandleFunc("/zarya/async/api/wanliu-order-reload-finished-hook", controller.WanliuOrderReloadFinishedHook)
	svr.AddHTTPHandleFunc("/zarya/async/api/wanliu-order-cancelled-after-strived-hook", controller.WanliuOrderCancelledAfterStrivedHook)
	svr.AddHTTPHandleFunc("/zarya/async/api/wanliu-intrip-order-update-notify-hook", controller.WanliuIntripOrderUpdateNotifyHook)
	svr.AddHTTPHandleFunc("/zarya/async/api/ibt_new_order_defer_msg_hook", controller.IbtNewOrderDeferMsgHook)

	svr.AddHTTPHandleFunc("/zarya/async/api/test-controller", controller.TestController)
	svr.AddHTTPHandleFunc("/zarya/async/api/ping", controller.Ping)
}
