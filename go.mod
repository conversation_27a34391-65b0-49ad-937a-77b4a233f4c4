module git.xiaojukeji.com/falcon/zarya-async-api

go 1.15

require (
	git.xiaojukeji.com/Elvish/elvish-lib-golang v1.2.27
	git.xiaojukeji.com/cc/uni-gateway-golang-sdk/v3 v3.5.1
	git.xiaojukeji.com/dirpc/dirpc-go-LocsvrThrift v1.0.0
	git.xiaojukeji.com/dirpc/dirpc-go-UserCenter v2.9.0+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-http-Ceres v1.1.22
	git.xiaojukeji.com/dirpc/dirpc-go-http-Dos v1.0.214
	git.xiaojukeji.com/dirpc/dirpc-go-http-Unique v1.0.7
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-DuseApiIntl v2.1.12+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-Locsvr v1.0.4
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-RouteBrokerService v2.0.19+incompatible
	git.xiaojukeji.com/disf/disf-go-spl-v2 v0.1.56
	git.xiaojukeji.com/falcon/copywriter-go-SDK v1.2.1
	git.xiaojukeji.com/falcon/fission-sdk v2.2.0+incompatible
	git.xiaojukeji.com/falcon/go-biz v1.51.2
	git.xiaojukeji.com/falcon/imc-go-sdk v0.2.9
	git.xiaojukeji.com/falcon/signpost-sdk-go/v2 v2.2.28
	git.xiaojukeji.com/falcon/zarya-core v0.2.1
	git.xiaojukeji.com/foundation/thrift v0.9.3
	git.xiaojukeji.com/gobiz/ctxutil v1.2.1
	git.xiaojukeji.com/gobiz/utils v1.0.1
	git.xiaojukeji.com/ibt-payment/paytype_sdk_go v1.0.1
	git.xiaojukeji.com/lego/context-go v3.3.8+incompatible
	git.xiaojukeji.com/lego/dirpc-go v1.25.5
	git.xiaojukeji.com/nuwa/binding v0.1.5
	git.xiaojukeji.com/nuwa/go-monitor v1.1.6
	git.xiaojukeji.com/nuwa/golibs/gormv2 v0.1.7
	git.xiaojukeji.com/nuwa/golibs/httpserver v0.2.2
	git.xiaojukeji.com/nuwa/golibs/redis v0.7.9
	git.xiaojukeji.com/nuwa/golibs/rpcserver/v2 v2.2.2
	git.xiaojukeji.com/nuwa/golibs/zerolog v1.3.26
	git.xiaojukeji.com/nuwa/protoc-dirpc v1.3.1
	github.com/go-sql-driver/mysql v1.8.1
	github.com/golang/protobuf v1.5.3
	github.com/grpc-ecosystem/grpc-gateway v1.16.0
	github.com/pkg/errors v0.9.1
	github.com/spf13/viper v1.10.1
	go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2 v2.7.7+incompatible
	go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2 v2.9.0
	go.intra.xiaojukeji.com/foundation/carrera-go-sdk v2.1.14+incompatible
	go.intra.xiaojukeji.com/platform-ha/onekey-degrade_sdk_go v3.2.9+incompatible
	golang.org/x/net v0.12.0
	google.golang.org/genproto/googleapis/api v0.0.0-20230706204954-ccb25ca9f130
	google.golang.org/grpc v1.57.0
	google.golang.org/protobuf v1.31.0
)

replace (
	golang.org/x/net => golang.org/x/net v0.0.0-20201224014010-6772e930b67b
	google.golang.org/grpc => google.golang.org/grpc v1.34.1
)
