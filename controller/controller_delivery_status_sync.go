package controller

import (
	"context"
	"net/http"

	BizIDL "git.xiaojukeji.com/falcon/go-biz/IDL"
	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/go-biz/utils/assert"
	HttpUtil "git.xiaojukeji.com/falcon/go-biz/utils/http"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/framework"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/services"
)

func DeliveryStatusSync(writer http.ResponseWriter, request *http.Request) {
	ctx := request.Context()

	// 解析binlog
	apiRequest, zErr := IDL.NewApiRequest(request)
	if zErr != nil {
		echoResponse(ctx, writer, zErr)
		return
	}

	// 服务分发
	dispatcher := getServiceDispatcher(ctx, apiRequest)
	if dispatcher == nil {
		echoResponse(ctx, writer, zerr.New(consts.ErrnoParamsError))
		return
	}

	// 执行服务主线
	framework.NewServiceDispatcher(dispatcher).Main(ctx, request, writer, apiRequest)
}

func getServiceDispatcher(ctx context.Context, apiRequest *IDL.ApiRequest) framework.ServiceInterface {
	if apiRequest.GetTable() == IDL.DeliveryPlatformOrderTable {
		log.Infof(ctx, "delivery_in_order", "delivery_id=%d", apiRequest.GetDeliveryID())
		return services.NewOrderService()
	}
	if apiRequest.GetTable() == IDL.DeliveryPlatformProcessTable {
		deliveryProcess := apiRequest.GetDeliveryProcess()
		status := deliveryProcess.Status
		subStatus := deliveryProcess.SubStatus
		log.Infof(ctx, "delivery_in_process", "driver_id=%d||delivery_id=%d||status=%d||sub_status=%d||prev_status=%d||prev_sub_status=%d",
			deliveryProcess.CurrRiderID, deliveryProcess.DeliveryID, status, subStatus, deliveryProcess.PrevStatus, deliveryProcess.PrevSubStatus)
		switch status {
		case consts.DeliveryUnassignStatus:
			// 未分配（创建订单、改派）
			return services.NewCreateService()
		case consts.DeliveryStriveStatus:
			if subStatus == 0 {
				// 120-0 引擎分单（但未成单）
				return services.NewDispatchService()
			}
			// 成单（120-1、121-2）
			return services.NewConfirmService()
		case consts.DeliveryArrivedStartStatus:
			// 到店
			return services.NewArrivedService()
		case consts.DeliveryTakeStatus:
			// 已取货（开始送货BeginCharge）
			return services.NewTakeService()
		case consts.DeliveryFinishStatus:
			// 完单
			return services.NewFinishService()
		case consts.DeliveryCancelStatus:
			// 取消
			return services.NewCancelService()
		case consts.DeliveryAbortStatus:
			// 关单
			return services.NewAbortService()
		default:
			// 中间状态（150、180、181、182、255）
			return services.NewDefaultService()
		}
	}
	return nil
}

func echoResponse(ctx context.Context, writer http.ResponseWriter, apiError *zerr.ZErr) {
	resp := &BizIDL.ServiceResponse{
		BasicResponse: &IDL.BaseResponse{
			Errno:  int(apiError.ErrNum()),
			Errmsg: apiError.ErrMsg(),
		},
	}

	if err := HttpUtil.EchoServiceJSON(writer, resp); assert.IsNotNil(err) {
		log.Fatalf(ctx, consts.ErrnoHttpEchoError, "failed to echo json,err=%s", err.Error())
	}
}
