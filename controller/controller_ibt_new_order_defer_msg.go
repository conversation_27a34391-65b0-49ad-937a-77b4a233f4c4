package controller

import (
	"net/http"

	"git.xiaojukeji.com/falcon/zarya-async-api/logic/ibt_new_order_defer_msg/framework"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/ibt_new_order_defer_msg/services"
)

func IbtNewOrderDeferMsgHook(writer http.ResponseWriter, request *http.Request) {
	//服务分发

	serviceDispatcher := framework.NewServiceDispatcher(services.NewDefaultService())
	ctx := request.Context()

	//执行服务主线
	serviceDispatcher.Main(ctx, request, writer)
}
