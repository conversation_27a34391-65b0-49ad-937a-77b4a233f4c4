package order_loader

import (
	"context"

	dosClient "git.xiaojukeji.com/falcon/go-biz/client/dos"
	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/go-biz/domain_model/order"
	"git.xiaojukeji.com/falcon/go-biz/sdk/dos"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_strived_hook/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_strived_hook/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Loader{}

type Loader struct {
	framework.BaseComponent
}

func (c *Loader) GetName() string {
	return "order_loader"
}

func (c *Loader) Run(ctx context.Context) error {
	/*
		if !c.Request().Type().IsBookingOrder() {
			return c.SetResponse(ctx, zerr.New(consts.Success), &IDL.BaseResponse{})
		}
	*/
	if c.Request().OrderId() == nil {
		return c.SetResponse(ctx, zerr.New(consts.Success), &IDL.BaseResponse{})
	}

	dm, err := c.loadOrderDM(ctx, c.Request().OrderId().GetLowOid(), c.Request().OrderId().GetDistrict())
	if err != nil {
		return err
	}

	c.BO().SetOrderDM(dm)
	return nil
}

func (l *Loader) loadOrderDM(ctx context.Context, lowOid int64, district string) (*order.DomainModel, error) {
	req := &dos.GetOrderInfoInput{
		OrderId:  lowOid,
		District: district,
		Caller:   consts.CommonCaller,
	}

	orderDM, err := dosClient.GetZaryaOrderInfo(ctx, req)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoDosError, "order info load  failed,err=%v, oid=%v", err, lowOid)
		return nil, l.ErrorResponse(ctx, consts.ErrnoParamsError)
	}

	return orderDM, nil
}
