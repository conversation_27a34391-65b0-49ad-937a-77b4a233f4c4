package driver_loader

import (
	"context"

	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_strived_hook/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Loader{}

type Loader struct {
	framework.BaseComponent
}

func (c *Loader) GetName() string {
	return "driver_loader"
}

func (c *Loader) Run(ctx context.Context) error {

	return nil
}
