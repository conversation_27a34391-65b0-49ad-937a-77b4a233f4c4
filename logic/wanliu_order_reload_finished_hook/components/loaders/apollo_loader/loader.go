package apollo_loader

import (
	"context"

	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/framework"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2"
)

type Loader struct {
	framework.BaseComponent
}

func (l *Loader) GetName() string {
	return "apollo_loader"
}

func (l *Loader) Run(ctx context.Context) error {
	ret, err := apollo.GetConfig("ibt_trading_matching", "imc_push_setting")
	if ret == nil || err != nil {
		return nil
	}

	todoConf := IDL.IMCConfig{}

	err = ret.GetJsonValue("domestic_departure_jp_imc", &todoConf)
	if err != nil {
		return nil
	}

	l.BO().SetIMCConfig(&todoConf)
	return nil
}
