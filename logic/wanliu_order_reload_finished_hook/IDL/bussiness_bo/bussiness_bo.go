package bussiness_bo

import (
	"context"
	"sync"

	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/IDL"

	"git.xiaojukeji.com/falcon/go-biz/domain_model/order"
)

// BusinessInfo
// 功能一、懒加载Feature：只允许在数据总线(BO)里进行feature的初始化,这样会有两个好处：
//
//	1.1) 数据总线掌握了这个接口所需的所有Feature，为后续在平台上可视化查看接口级别Feature总览提供条件
//	1.2) BO内部统一进行懒加载，收敛Feature的初始化方式，避免各处随意初始化，导致bug。
//
// 功能二、数据传递：方便数据在各个组件之间传递，比如Loader加载数据，Render、Event消费数据等,
//
//	避免数据无序传递。如果有各个业务线，那么最好进行业务线区分，比如 VamosBO, MiniBusBO
//
// 功能三、语法糖，因为golang nil pointer会导致panic,并且我们的BO内部大量的数据都需要做判空处理（可能组件未加载，未命中，异常）
// 那么如果直接读取BO的数据，可能会要写一堆的
//
//	if data:= bo.GetPriceInfo(); data!=nil {
//	   xxxx逻辑
//	}
//
// 这样无疑很麻烦，所以建议直接放到BO里提供语法糖，方便调用，请参考  IsCompassDuseInfoEmpty, IsCustom 等
type BusinessInfo struct {
	//内部塞了一个ctx,并不提供getter，主要目的是有些内部的函数出现异常，可以方便的记录日志（因为记录日志必须有ctx参数)
	//同时也可以避免这些内部函数需要传ctx搞得很恶心。 参考 UpdateExtraType
	ctx context.Context

	//读写互斥锁,这里主要考虑到golang是支持并发的，对于loader、validator、event，大部分情况都可以进行并发执行（有依赖关系除外)
	//那么在这种情况下，可能存在着并发读写问题，这个时候需要用互斥锁进行处理,防止并发异常。
	rwLock sync.RWMutex

	orderDM *order.DomainModel

	imcConfig *IDL.IMCConfig
}

func NewBusinessInfo() *BusinessInfo {
	return &BusinessInfo{}
}

func (b *BusinessInfo) SetCtx(ctx context.Context) *BusinessInfo {
	b.ctx = ctx
	return b
}

// SetOrderDM 订单领域模型，当前订单信息
func (b *BusinessInfo) SetOrderDM(dm *order.DomainModel) {
	b.rwLock.Lock()
	defer b.rwLock.Unlock()
	b.orderDM = dm
}

// GetOrderDM 订单领域模型，当前订单信息
func (b *BusinessInfo) OrderDM() *order.DomainModel {
	return b.orderDM
}

func (b *BusinessInfo) SetIMCConfig(IDL *IDL.IMCConfig) {
	b.rwLock.Lock()
	defer b.rwLock.Unlock()
	b.imcConfig = IDL
}

// GetModuleTypeConfig 获取指定模块和类型的配置
func (b *BusinessInfo) GetModuleTypeConfig(module, configType string) *IDL.IMCModuleConfig {
	if b.imcConfig == nil {
		return nil
	}
	if _, ok := (*b.imcConfig)[module]; !ok {
		return nil
	}
	configData := (*b.imcConfig)[module]

	// 查找匹配的配置
	for _, config := range configData {
		if config.Type == configType {
			// 如果 IsPush 为 1，表示不发送通知，返回 nil
			if config.IsPush == 1 {
				return nil
			}
			return config
		}
	}
	return nil
}
