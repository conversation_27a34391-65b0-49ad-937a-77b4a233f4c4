package feature_bo

// 该文件代码依赖工具生成，请不要手动改
// 该文件代码依赖工具生成，请不要手动改
// 该文件代码依赖工具生成，请不要手动改

import (
	BookingOrder_WanliuOrderReloadFinishedHook_Idl "git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/booking_order/wanliu_order_reload_finished_hook/IDL"
	JwOrder_WanliuOrderReloadFinishedHook_Idl "git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/jw_order/wanliu_order_reload_finished_hook/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/IDL/base_object"
	componentsV2 "git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

type FeatureInstancesSet struct {
	BookingOrder *BookingOrder_WanliuOrderReloadFinishedHook_Idl.BusinessInfo

	JwOrder *JwOrder_WanliuOrderReloadFinishedHook_Idl.BusinessInfo
}

func NewFeatureBusinessInfo() *FeatureInstancesSet {
	return &FeatureInstancesSet{

		BookingOrder: &BookingOrder_WanliuOrderReloadFinishedHook_Idl.BusinessInfo{
			BaseFeatureBusinessObject: base_object.BaseFeatureBusinessObject{
				&componentsV2.FeatureInstance{},
			},
		},

		JwOrder: &JwOrder_WanliuOrderReloadFinishedHook_Idl.BusinessInfo{
			BaseFeatureBusinessObject: base_object.BaseFeatureBusinessObject{
				&componentsV2.FeatureInstance{},
			},
		},
	}
}
