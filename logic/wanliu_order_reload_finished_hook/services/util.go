package services

// 该文件代码依赖工具生成，请不要手动改
// 该文件代码依赖工具生成，请不要手动改
// 该文件代码依赖工具生成，请不要手动改

import (
	BookingOrder_WanliuOrderReloadFinishedHook "git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/booking_order/wanliu_order_reload_finished_hook"
	JwOrder_WanliuOrderReloadFinishedHook "git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/jw_order/wanliu_order_reload_finished_hook"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/IDL/feature_bo"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/framework"
	componentsV2 "git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

// ComponentConf 配置类数据结构
type ComponentConf struct {
	Name      string
	Component componentsV2.ComponentInterface

	// baseConf
	IsBreak bool
	IsAsync bool

	// feature
	FeatureName string
}

type ExecutionUnitConf struct {
	DispatchFunc  componentsV2.DispatchFunc
	ComponentList []ComponentConf
}

// BaseJoinPointComponent 就是 ComponentInterface 接口，去掉 run方法。因为 joinPoint component 要实现的run方法和一般 component 不一样。
// BaseJoinPointComponent 额外还添加了一个  Init 方法，方便后面初始化
type BaseJoinPointComponent interface {
	CanAsync() bool
	CanBreak() bool
	LoadConfig(name string, conf map[string]interface{})
	GetName() string
	framework.ComponentInitializer
}

// 组装service 用工具函数
func executionUnitConfToInstance(unitConfigs []ExecutionUnitConf, featureSet *feature_bo.FeatureInstancesSet) []componentsV2.ExecutionUnit {
	ret := []componentsV2.ExecutionUnit{}
	for _, unitConf := range unitConfigs {
		unit := componentsV2.ExecutionUnit{
			DispatchFunc: unitConf.DispatchFunc,
		}
		for _, comConf := range unitConf.ComponentList {
			component := comConf.Component
			fillFeatureBusinessInfo(component, featureSet)
			conf := map[string]interface{}{
				"is_break": comConf.IsBreak,
				"is_async": comConf.IsAsync,
			}
			component.LoadConfig(comConf.Name, conf)

			unit.Components = append(unit.Components, component)
		}
		ret = append(ret, unit)
	}

	return ret
}

func collectAndBuildInstance(eUnitSet componentsV2.EUnitSet, unitConfigs []ExecutionUnitConf, featureSet *feature_bo.FeatureInstancesSet) componentsV2.EUnitSet {

	return eUnitSet

}

func fillFeatureBusinessInfo(component interface{}, instanceSet *feature_bo.FeatureInstancesSet) {
	switch featureCom := component.(type) {
	case *BookingOrder_WanliuOrderReloadFinishedHook.FeatureComponent_BookingOrderIMC:
		featureCom.FeatureBO = instanceSet.BookingOrder
	case *JwOrder_WanliuOrderReloadFinishedHook.FeatureComponent_JWTaxiIMC:
		featureCom.FeatureBO = instanceSet.JwOrder
	}
}

// joinPoint component 适配器结构
