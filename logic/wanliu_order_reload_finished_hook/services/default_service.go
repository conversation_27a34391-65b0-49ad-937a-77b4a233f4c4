package services

// 该文件代码依赖工具生成，请不要手动改
// 该文件代码依赖工具生成，请不要手动改
// 该文件代码依赖工具生成，请不要手动改

import (
	BookingOrder_WanliuOrderReloadFinishedHook "git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/booking_order/wanliu_order_reload_finished_hook"
	JwOrder_WanliuOrderReloadFinishedHook "git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/jw_order/wanliu_order_reload_finished_hook"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/IDL/feature_bo"
	Events_DosEvent "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/components/events/dos_event"
	Loaders_ApolloLoader "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/components/loaders/apollo_loader"
	Loaders_OrderLoader "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/components/loaders/order_loader"
	Renders_BaseRender "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/components/renders/base_render"
	componentsV2 "git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
	Dispatchers_AsyncDispatcher "git.xiaojukeji.com/falcon/zarya-core/frameworkV2/dispatchers/async_dispatcher"
)

type DefaultService struct {
}

func NewDefaultService() *DefaultService {
	return &DefaultService{}
}

func (d *DefaultService) LoadersV2() []ExecutionUnitConf {
	return []ExecutionUnitConf{
		{

			ComponentList: []ComponentConf{
				{
					Name:      "loaders/order_loader",
					Component: &Loaders_OrderLoader.Loader{},
					IsBreak:   true,
				},
				{
					Name:      "loaders/apollo_loader",
					Component: &Loaders_ApolloLoader.Loader{},
					IsBreak:   true,
				}},
		}}
}

func (d *DefaultService) ValidatorsV2() []ExecutionUnitConf {
	return []ExecutionUnitConf{
		{

			ComponentList: []ComponentConf{},
		}}
}

func (d *DefaultService) OperatorsV2() []ExecutionUnitConf {
	return []ExecutionUnitConf{
		{

			ComponentList: []ComponentConf{},
		}}
}

func (d *DefaultService) RendersV2() []ExecutionUnitConf {
	return []ExecutionUnitConf{
		{

			ComponentList: []ComponentConf{
				{
					Name:      "renders/base_render",
					Component: &Renders_BaseRender.Render{},
					IsBreak:   true,
				}},
		}}
}

func (d *DefaultService) EventsV2() []ExecutionUnitConf {
	return []ExecutionUnitConf{
		{

			DispatchFunc:  Dispatchers_AsyncDispatcher.Dispatcher,
			ComponentList: []ComponentConf{},
		},
		{

			ComponentList: []ComponentConf{
				{
					Name:      "events/dos_event",
					Component: &Events_DosEvent.Event{},
				},
				{
					Name:      "",
					Component: &BookingOrder_WanliuOrderReloadFinishedHook.FeatureComponent_BookingOrderIMC{},
				},
				{
					Name:      "",
					Component: &JwOrder_WanliuOrderReloadFinishedHook.FeatureComponent_JWTaxiIMC{},
				}},
		}}
}

// GetExecutionUnitMap 这个方法里的执行顺序不要轻易改变 Loader => Validators => Operators => Renders
// => Events => Finals
func (d *DefaultService) GetExecutionUnitMap(featureSet *feature_bo.FeatureInstancesSet) componentsV2.EUnitSet {
	ret := componentsV2.EUnitSet{}

	loadersConf := d.LoadersV2()
	ret["loaders"] = executionUnitConfToInstance(loadersConf, featureSet)
	ret = collectAndBuildInstance(ret, loadersConf, featureSet)

	validatorsConf := d.ValidatorsV2()
	ret["validators"] = executionUnitConfToInstance(validatorsConf, featureSet)
	ret = collectAndBuildInstance(ret, validatorsConf, featureSet)

	operatorsConf := d.OperatorsV2()
	ret["operators"] = executionUnitConfToInstance(operatorsConf, featureSet)
	ret = collectAndBuildInstance(ret, operatorsConf, featureSet)

	rendersConf := d.RendersV2()
	ret["renders"] = executionUnitConfToInstance(rendersConf, featureSet)
	ret = collectAndBuildInstance(ret, rendersConf, featureSet)

	eventsConf := d.EventsV2()
	ret["events"] = executionUnitConfToInstance(eventsConf, featureSet)
	ret = collectAndBuildInstance(ret, eventsConf, featureSet)

	return ret
}

func (d *DefaultService) GetName() string {
	return "DefaultService"
}
