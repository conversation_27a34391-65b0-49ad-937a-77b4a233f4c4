package framework

import (
	"context"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

// BaseComponent 接口级别的组件BASE类，用于进行
type BaseComponent struct {
	components.DefaultComponent
	//当前组件所属的服务总线模板(包含各类BO、DTO、骨架的核心代码),匿名在此，用于组件直接获取总线的方法进行请求级别的数据处理
	//比如设置response，获取BO、等等
	*ServiceDispatcher
}

// Init 组件的构造函数,用于进行依赖处理
func (c *BaseComponent) Init(service *ServiceDispatcher) {
	c.ServiceDispatcher = service
}

type ComponentInitializer interface {
	Init(service *ServiceDispatcher)
	LoadConfig(name string, conf map[string]interface{})
}

func (c *BaseComponent) JoinPoint(ctx context.Context, name string, params ...interface{}) {
	units := c.executionUnitMap[name]
	for _, unit := range units {
		for _, component := range unit.Components {
			stubComponent, ok := component.(components.FeatureStubComponent)
			if ok {
				stubComponent.SetStubParams(params)
			}
		}
	}
	c.componentEngine.Exec(ctx, units)

}

func (c *BaseComponent) SkipComponent(name string) {

}
