package IDL

import notifyTopic "git.xiaojukeji.com/falcon/zarya-async-api/models/ddmq/intrip_order_update_notify_topic"

const (
	StartingPointIcon     = "https://img0.didiglobal.com/static/gstar/img/GOBQnnRlQA1738995903464.png"
	StartingPointIconDark = "https://img0.didiglobal.com/static/gstar/img/kGDhV0v3nQ1738995937171.png"
	EndpointIcon          = "https://img0.didiglobal.com/static/gstar/img/7o2Pv3wgv31738995951900.png"
	EndpointIconDark      = "https://img0.didiglobal.com/static/gstar/img/0jJdPO2M7H1738995961196.png"
	WaypointIcon          = "https://img0.didiglobal.com/static/gstar/img/1u0yXJloij1738995969477.png"
	WaypointIconDark      = "https://img0.didiglobal.com/static/gstar/img/9LkHfweESX1738995973300.png"
)

const (
	ContentTypeStartingPoint = 1
	ContentTypeEndPoint      = 2
	ContentTypeWaypoint      = 3
)

const (
	RiskyBgColor       = "#F2183D"
	RiskyBgColorDark   = "#CC2944"
	RiskyFontColor     = "#FFFFFF"
	RiskyFontColorDark = "#333333"
	RiskyIcon          = "https://img0.didiglobal.com/static/gstar/img/GNkBfsADpO1738996029152.png"
	RiskyIconDark      = "https://img0.didiglobal.com/static/gstar/img/BvXbFdnLAw1738996034540.png"
)

var NotifyTypeToContentType = map[int]int{
	notifyTopic.NotifyTypeStartingPoint: ContentTypeStartingPoint,
	notifyTopic.NotifyTypeEndpoint:      ContentTypeEndPoint,
	notifyTopic.NotifyTypeWaypoint:      ContentTypeWaypoint,
}

var ContentLeftMap map[int]*ContentLeft = map[int]*ContentLeft{
	ContentTypeStartingPoint: {
		Icon:     StartingPointIcon,
		IconDark: StartingPointIconDark,
	},
	ContentTypeEndPoint: {
		Icon:     EndpointIcon,
		IconDark: EndpointIconDark,
	},
	ContentTypeWaypoint: {
		Icon:     WaypointIcon,
		IconDark: WaypointIconDark,
	},
}

type PushInfo struct {
	BizType   int    `json:"biz_type"`
	OrderInfo string `json:"order_info"`
	PopupInfo string `json:"popup_info"`
}
type OrderInfo struct {
	Oid            string        `json:"oid"`
	ToAddress      string        `json:"to_address"`
	ToLat          string        `json:"to_lat"`
	ToLng          string        `json:"to_lng"`
	ToName         string        `json:"to_name"`
	FromAddress    string        `json:"from_address"`
	FromLat        string        `json:"from_lat"`
	FromLng        string        `json:"from_lng"`
	FromName       string        `json:"from_name"`
	WayPointInfo   *WayPointInfo `json:"way_point_info"`
	IsOfflinePay   int           `json:"is_offline_pay"`
	PaymentView    *PaymentView  `json:"payment_view"`
	DriverLateTime int64         `json:"driver_late_time"`
}

type WayPointInfo struct {
	StopoverPoint    []*StopoverPointInfo `json:"stopover_point"`
	WayPointsVersion string               `json:"way_points_version"`
}

type PaymentView struct {
	BackgroundColor string `json:"background_color"`
	FontColor       string `json:"font_color"`
	PayText         string `json:"pay_text"`
	PayIcon         string `json:"pay_icon"`
}

type PopupInfo struct {
	Title       string         `json:"title"`
	Tts         string         `json:"tts"`
	Countdown   int            `json:"countdown"`
	ContentArea []*ContentItem `json:"content_area"`
}

type ContentItem struct {
	Type         int           `json:"type"`
	Content      string        `json:"content"`
	ContentLeft  *ContentLeft  `json:"content_left,omitempty"`
	ContentRight *ContentRight `json:"content_right,omitempty"`
}

type ContentLeft struct {
	Icon     string `json:"icon"`
	IconDark string `json:"icon_dark"`
}

type ContentRight struct {
	BgColor       string `json:"bg_color"`
	BgColorDark   string `json:"bg_color_dark"`
	FontColor     string `json:"font_color"`
	FontColorDark string `json:"font_color_dark"`
	Text          string `json:"text"`
	Icon          string `json:"icon"`
	IconDark      string `json:"icon_dark"`
}
type StopoverPointInfo struct {
	Address     string  `json:"address"`
	AddressAll  string  `json:"address_all"`
	CountryCode string  `json:"country_code"`
	Lat         float64 `json:"lat"`
	Lng         float64 `json:"lng"`
	Name        string  `json:"name"`
	PoiId       string  `json:"poi_id"`
	Status      int     `json:"status"`
}
