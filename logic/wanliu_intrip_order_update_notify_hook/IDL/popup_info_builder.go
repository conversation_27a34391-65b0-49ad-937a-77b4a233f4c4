package IDL

import (
	"context"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts/i18keys"
	notifyTopic "git.xiaojukeji.com/falcon/zarya-async-api/models/ddmq/intrip_order_update_notify_topic"
	"strings"
)

const PopupWindowCountDown = 10

type PopupInfoBuilder interface {
	buildTitle(ctx context.Context) string
	buildTts(ctx context.Context) string
	buildContent(ctx context.Context) []*ContentItem

	Build(ctx context.Context) *PopupInfo
}

type DefaultPopupBuilder struct {
	operationInfo *OperationInfo
	notifyType    int
	oldPointInfos []*PointInfo
	newPointInfos []*PointInfo
	lang          string
	remindType    int
	riskyOpen     bool

	income *IncomeMsg
}

func NewPopupBuilder(notifyType int, operationInfo *OperationInfo, lang string, remindType int,
	oldPointInfos, newPointInfos []*PointInfo, incomeMsg *IncomeMsg, riskyOpen bool) PopupInfoBuilder {
	if notifyType == notifyTopic.NotifyTypePayType {
		return newPaymentPopInfoBuilder(lang)
	}

	defaultBuilder := newDefaultPopupBuilder(notifyType, operationInfo, lang, remindType, oldPointInfos, newPointInfos, incomeMsg, riskyOpen)
	if notifyType == notifyTopic.NotifyTypeEndpointAndWaypoint {
		return newWaypointAndEndpointPopupBuilder(defaultBuilder)
	}

	return newDefaultPopupBuilder(notifyType, operationInfo, lang, remindType, oldPointInfos, newPointInfos, incomeMsg, riskyOpen)
}

func newDefaultPopupBuilder(notifyType int, operationInfo *OperationInfo, lang string, remindType int,
	oldPointInfos, newPointInfos []*PointInfo, incomeMsg *IncomeMsg, riskyOpen bool) *DefaultPopupBuilder {
	return &DefaultPopupBuilder{
		operationInfo: operationInfo,
		notifyType:    notifyType,
		oldPointInfos: oldPointInfos,
		newPointInfos: newPointInfos,
		lang:          lang,
		remindType:    remindType,
		income:        incomeMsg,
		riskyOpen:     riskyOpen,
	}
}

func (c *DefaultPopupBuilder) buildTitle(ctx context.Context) string {

	titleKey := titleI18nKeyMap[c.notifyType][c.operationInfo.OperationType].singleKey
	if c.operationInfo.ChangeNum > 1 {
		titleKey = titleI18nKeyMap[c.notifyType][c.operationInfo.OperationType].doubleKey
	}

	title := titleKey.Copywriter(ctx, c.lang, map[string]interface{}{"num": c.operationInfo.ChangeNum})

	if c.income == nil {
		return title
	}

	incomeMsg := c.income.Translate(ctx, c.notifyType, c.lang)

	sb := strings.Builder{}
	sb.WriteString(title)
	if incomeMsg != "" {
		sb.WriteString(" ")
		sb.WriteString(incomeMsg)
	}

	return sb.String()
}

func (c *DefaultPopupBuilder) buildTts(ctx context.Context) string {
	//请注意
	return i18keys.I18nKeyPleaseNote.Copywriter(ctx, c.lang, nil)

	//播报详细地址
	//ttsKey := ttsI18nKeyMap[c.notifyType][c.operationInfo.OperationType].singleKey
	//if c.operationInfo.ChangeNum > 1 {
	//	ttsKey = ttsI18nKeyMap[c.notifyType][c.operationInfo.OperationType].doubleKey
	//}
	//
	//var strBuilder strings.Builder
	//updatePoints := c.getUpdatePoints()
	//for i := 0; i < len(updatePoints); i++ {
	//	strBuilder.WriteString(updatePoints[i].GetCommunityAddress())
	//	strBuilder.WriteString(",")
	//}
	//updatePointsStr := strBuilder.String()
	//
	//tts := ttsKey.Copywriter(ctx, c.lang, map[string]interface{}{"address": strings.TrimRight(updatePointsStr, ",")})
	//
	//return tts
}

func (c *DefaultPopupBuilder) buildContent(ctx context.Context) []*ContentItem {
	//只有新增或者修改需要展示content
	if (c.operationInfo.OperationType != notifyTopic.OperationAdd) && (c.operationInfo.OperationType != notifyTopic.OperationModify) {
		return nil
	}

	updatePoints := c.getUpdatePoints()
	numUpdate := len(updatePoints)

	var content []*ContentItem
	for i := 0; i < numUpdate; i++ {
		contentItem := c.buildContentItem(ctx, updatePoints[i].GetCommunityAddress(), updatePoints[i].IsRisky(), updatePoints[i].Type)
		content = append(content, contentItem)
	}

	return content
}

func (c *DefaultPopupBuilder) Build(ctx context.Context) *PopupInfo {
	title := c.buildTitle(ctx)
	tts := c.buildTts(ctx)

	return &PopupInfo{
		Title:       title,
		Tts:         title + "," + tts,
		Countdown:   PopupWindowCountDown,
		ContentArea: c.buildContent(ctx),
	}
}

func newWaypointAndEndpointPopupBuilder(b *DefaultPopupBuilder) *WaypointAndEndpointPopupBuilder {
	return &WaypointAndEndpointPopupBuilder{defaultBuilder: b}
}

type WaypointAndEndpointPopupBuilder struct {
	defaultBuilder *DefaultPopupBuilder
}

func (c *WaypointAndEndpointPopupBuilder) buildTitle(ctx context.Context) string {
	panic("should not use")
}

func (c *WaypointAndEndpointPopupBuilder) buildContent(ctx context.Context) []*ContentItem {
	if c.defaultBuilder.operationInfo.OperationType == notifyTopic.OperationNoChange {
		return nil
	}

	updatePoints := c.defaultBuilder.getUpdatePoints()
	numUpdate := len(updatePoints)

	var content []*ContentItem
	for i := 0; i < numUpdate; i++ {
		contentItem := c.defaultBuilder.buildContentItem(ctx, updatePoints[i].GetCommunityAddress(), updatePoints[i].IsRisky(), updatePoints[i].Type)
		content = append(content, contentItem)
	}

	return content
}

func (c *WaypointAndEndpointPopupBuilder) buildTts(ctx context.Context) string {
	ttsKey := ttsI18nKeyMap[c.defaultBuilder.notifyType][c.defaultBuilder.operationInfo.OperationType].singleKey
	if c.defaultBuilder.operationInfo.ChangeNum > 1 {
		ttsKey = ttsI18nKeyMap[c.defaultBuilder.notifyType][c.defaultBuilder.operationInfo.OperationType].doubleKey
	}

	var strBuilder strings.Builder
	var endpointAddress, updatePointsStr string

	updatePoints := c.defaultBuilder.getUpdatePoints()
	for i := 0; i < len(updatePoints); i++ {
		if updatePoints[i].Type == ContentTypeEndPoint {
			endpointAddress = updatePoints[i].GetCommunityAddress()
		} else {
			strBuilder.WriteString(updatePoints[i].GetCommunityAddress())
			strBuilder.WriteString(",")
		}
	}
	updatePointsStr = strBuilder.String()

	if c.defaultBuilder.operationInfo.OperationType == notifyTopic.OperationAdd || c.defaultBuilder.operationInfo.OperationType == notifyTopic.OperationModify {
		tts := ttsKey.Copywriter(ctx, c.defaultBuilder.lang, map[string]interface{}{"address": strings.TrimRight(updatePointsStr, ","), "address1": endpointAddress})
		return tts
	}

	if c.defaultBuilder.operationInfo.OperationType == notifyTopic.OperationDelete || c.defaultBuilder.operationInfo.OperationType == notifyTopic.OperationReduce {
		tts := ttsKey.Copywriter(ctx, c.defaultBuilder.lang, map[string]interface{}{"address": endpointAddress})
		return tts
	}

	return ""
}

func (c *WaypointAndEndpointPopupBuilder) Build(ctx context.Context) *PopupInfo {
	title := c.defaultBuilder.buildTitle(ctx)
	tts := c.defaultBuilder.buildTts(ctx)

	return &PopupInfo{
		Title:       title,
		Tts:         title + "," + tts,
		Countdown:   PopupWindowCountDown,
		ContentArea: c.buildContent(ctx),
	}
}

type PaymentPopInfoBuilder struct {
	lang string
}

func newPaymentPopInfoBuilder(lang string) *PaymentPopInfoBuilder {
	return &PaymentPopInfoBuilder{lang: lang}
}

func (p *PaymentPopInfoBuilder) buildTitle(ctx context.Context) string {
	return i18keys.I18nKeyPaxChangePaymentMethod.Copywriter(ctx, p.lang, nil)
}

func (p *PaymentPopInfoBuilder) buildTts(ctx context.Context) string {
	title := i18keys.I18nKeyPaxChangePaymentMethod.Copywriter(ctx, p.lang, nil)
	pleaseNote := i18keys.I18nKeyPleaseNote.Copywriter(ctx, p.lang, nil)
	sb := strings.Builder{}
	sb.WriteString(title)
	sb.WriteString(",")
	sb.WriteString(pleaseNote)

	return sb.String()
}

func (p *PaymentPopInfoBuilder) buildContent(ctx context.Context) []*ContentItem {
	return nil
}

func (p *PaymentPopInfoBuilder) Build(ctx context.Context) *PopupInfo {
	return &PopupInfo{
		Title:     p.buildTitle(ctx),
		Tts:       p.buildTts(ctx),
		Countdown: PopupWindowCountDown,
	}
}

// 获取有变化的地点 new相对于old的变化，为空说明是new相对old是减少或者删除
func (c *DefaultPopupBuilder) getUpdatePoints() []*PointInfo {
	var ret []*PointInfo

	mb := make(map[string]bool)
	for _, oldInfo := range c.oldPointInfos {
		mb[oldInfo.Data.PoiId] = true
	}

	//查找new是否在old中
	for _, newInfo := range c.newPointInfos {
		if _, found := mb[newInfo.Data.PoiId]; !found {
			ret = append(ret, newInfo)
		}
	}

	return ret
}

func (c *DefaultPopupBuilder) buildContentItem(ctx context.Context, contentText string, isRisky bool, contentType int) *ContentItem {
	contentItem := &ContentItem{
		Type:        contentType,
		Content:     contentText,
		ContentLeft: ContentLeftMap[contentType],
	}

	if isRisky {
		contentItem.ContentRight = &ContentRight{
			BgColor:       RiskyBgColor,
			BgColorDark:   RiskyBgColorDark,
			FontColor:     RiskyFontColor,
			FontColorDark: RiskyFontColorDark,
			Text:          i18keys.I18nkKeyRisky.Copywriter(ctx, c.lang, nil),
			Icon:          RiskyIcon,
			IconDark:      RiskyIconDark,
		}
	}

	return contentItem
}
