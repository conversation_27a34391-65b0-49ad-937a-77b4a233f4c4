package IDL

import (
	"context"
	"git.xiaojukeji.com/falcon/go-biz/domain_model/order"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts/i18keys"
)

type OrderInfoBuilder struct {
	orderDm        *order.DomainModel
	lang           string
	isOfflinePay   bool
	driverLateTime int64
}

func NewOrderInfoBuilder(orderDm *order.DomainModel, lang string, isOfflinePay bool, time int64) *OrderInfoBuilder {
	return &OrderInfoBuilder{orderDm: orderDm, lang: lang, isOfflinePay: isOfflinePay, driverLateTime: time}
}

func (b *OrderInfoBuilder) Build(ctx context.Context) *OrderInfo {

	return &OrderInfo{
		Oid:            b.orderDm.OrderId().GetEncodeOid(),
		ToAddress:      b.orderDm.ToAddress(),
		ToLat:          b.orderDm.DestLat().ToString(),
		ToLng:          b.orderDm.DestLng().ToString(),
		ToName:         b.orderDm.ToName(),
		FromAddress:    b.orderDm.FromAddress(),
		FromLat:        b.orderDm.StartingLat().ToString(),
		FromLng:        b.orderDm.StartingLng().ToString(),
		FromName:       b.orderDm.FromName(),
		WayPointInfo:   b.buildWaypointInfo(),
		IsOfflinePay:   converter.BoolToInt(b.isOfflinePay),
		PaymentView:    b.buildPaymentView(ctx),
		DriverLateTime: b.driverLateTime,
	}
}

func (b *OrderInfoBuilder) buildWaypointInfo() *WayPointInfo {
	waypointInfo := &WayPointInfo{
		WayPointsVersion: b.orderDm.WayPointsVersion(),
	}

	if b.orderDm.WayPointsAInfo() != nil && b.orderDm.WayPointsAInfo().GetPoiId() != "" {
		waypointA := &StopoverPointInfo{
			Address:     b.orderDm.WayPointsAInfo().GetAddress(),
			AddressAll:  b.orderDm.WayPointsAInfo().GetAddressAll(),
			CountryCode: b.orderDm.WayPointsAInfo().GetCountryCode(),
			Lat:         b.orderDm.WayPointsAInfo().GetLat().ToFloat64(),
			Lng:         b.orderDm.WayPointsAInfo().GetLng().ToFloat64(),
			Name:        b.orderDm.WayPointsAInfo().GetName(),
			PoiId:       b.orderDm.WayPointsAInfo().GetPoiId(),
			Status:      b.orderDm.WayPointsAStatus().ToInt(),
		}
		waypointInfo.StopoverPoint = append(waypointInfo.StopoverPoint, waypointA)
	}

	if b.orderDm.WayPointsBInfo() != nil && b.orderDm.WayPointsBInfo().GetPoiId() != "" {
		waypointB := &StopoverPointInfo{
			Address:     b.orderDm.WayPointsBInfo().GetAddress(),
			AddressAll:  b.orderDm.WayPointsBInfo().GetAddressAll(),
			CountryCode: b.orderDm.WayPointsBInfo().GetCountryCode(),
			Lat:         b.orderDm.WayPointsBInfo().GetLat().ToFloat64(),
			Lng:         b.orderDm.WayPointsBInfo().GetLng().ToFloat64(),
			Name:        b.orderDm.WayPointsBInfo().GetName(),
			PoiId:       b.orderDm.WayPointsBInfo().GetPoiId(),
			Status:      b.orderDm.WayPointsBStatus().ToInt(),
		}
		waypointInfo.StopoverPoint = append(waypointInfo.StopoverPoint, waypointB)
	}

	return waypointInfo
}

func (b *OrderInfoBuilder) buildPaymentView(ctx context.Context) *PaymentView {
	if !b.isOfflinePay {
		return &PaymentView{
			BackgroundColor: "142696FF",
			FontColor:       "#2696FF",
			PayText:         i18keys.I18nKeyOnlinePayment.Copywriter(ctx, b.lang, nil),
			PayIcon:         "https://img0.didiglobal.com/static/gstar/img/N0BpXJQWua1597208148193.png",
		}
	}
	return nil
}
