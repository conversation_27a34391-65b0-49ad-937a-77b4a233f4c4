package services

// 该文件代码依赖工具生成，请不要手动改
// 该文件代码依赖工具生成，请不要手动改
// 该文件代码依赖工具生成，请不要手动改
// 生成时间:2025-02-28 13:19:30.666438 +0800 CST m=+1.618566376

import (
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_intrip_order_update_notify_hook/IDL/feature_bo"
	Loaders_CeresLoader "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_intrip_order_update_notify_hook/components/loaders/ceres_loader"
	Loaders_DriverLoader "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_intrip_order_update_notify_hook/components/loaders/driver_loader"
	Loaders_OperationInfoLoader "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_intrip_order_update_notify_hook/components/loaders/operation_info_loader"
	Loaders_OrderLoader "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_intrip_order_update_notify_hook/components/loaders/order_loader"
	Loaders_PointInfosLoader "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_intrip_order_update_notify_hook/components/loaders/point_infos_loader"
	Loaders_RiskyLoader "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_intrip_order_update_notify_hook/components/loaders/risky_loader"
	Loaders_SignpostLoader "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_intrip_order_update_notify_hook/components/loaders/signpost_loader"
	Operators_PushOperator "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_intrip_order_update_notify_hook/components/operators/push_operator"
	Renders_BaseRender "git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_intrip_order_update_notify_hook/components/renders/base_render"
	componentsV2 "git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
	Dispatchers_DefaultParallel "git.xiaojukeji.com/falcon/zarya-core/frameworkV2/dispatchers/default_parallel"
)

type DefaultService struct {
}

func NewDefaultService() *DefaultService {
	return &DefaultService{}
}

func (d *DefaultService) LoadersV2() []ExecutionUnitConf {
	return []ExecutionUnitConf{
		{

			DispatchFunc: Dispatchers_DefaultParallel.Dispatcher,
			ComponentList: []ComponentConf{
				{
					Name:      "loaders/driver_loader",
					Component: &Loaders_DriverLoader.Loader{},
					IsBreak:   true,
				},
				{
					Name:      "loaders/operation_info_loader",
					Component: &Loaders_OperationInfoLoader.Loader{},
					IsBreak:   true,
				}},
		},
		{

			ComponentList: []ComponentConf{
				{
					Name:      "loaders/order_loader",
					Component: &Loaders_OrderLoader.Loader{},
					IsBreak:   true,
				}},
		},
		{

			DispatchFunc: Dispatchers_DefaultParallel.Dispatcher,
			ComponentList: []ComponentConf{
				{
					Name:      "loaders/ceres_loader",
					Component: &Loaders_CeresLoader.Loader{},
				},
				{
					Name:      "loaders/point_infos_loader",
					Component: &Loaders_PointInfosLoader.Loader{},
				},
				{
					Name:      "loaders/signpost_loader",
					Component: &Loaders_SignpostLoader.Loader{},
				}},
		},
		{

			ComponentList: []ComponentConf{
				{
					Name:      "loaders/risky_loader",
					Component: &Loaders_RiskyLoader.Loader{},
				}},
		}}
}

func (d *DefaultService) OperatorsV2() []ExecutionUnitConf {
	return []ExecutionUnitConf{
		{

			ComponentList: []ComponentConf{
				{
					Name:      "operators/push_operator",
					Component: &Operators_PushOperator.Operator{},
				}},
		}}
}

func (d *DefaultService) RendersV2() []ExecutionUnitConf {
	return []ExecutionUnitConf{
		{

			ComponentList: []ComponentConf{
				{
					Name:      "renders/base_render",
					Component: &Renders_BaseRender.Render{},
				}},
		}}
}

// GetExecutionUnitMap 这个方法里的执行顺序不要轻易改变 Loader => Validators => Operators => Renders
// => Events => Finals
func (d *DefaultService) GetExecutionUnitMap(featureSet *feature_bo.FeatureInstancesSet) componentsV2.EUnitSet {
	ret := componentsV2.EUnitSet{}

	loadersConf := d.LoadersV2()
	ret["loaders"] = executionUnitConfToInstance(loadersConf, featureSet)
	ret = collectAndBuildInstance(ret, loadersConf, featureSet)

	operatorsConf := d.OperatorsV2()
	ret["operators"] = executionUnitConfToInstance(operatorsConf, featureSet)
	ret = collectAndBuildInstance(ret, operatorsConf, featureSet)

	rendersConf := d.RendersV2()
	ret["renders"] = executionUnitConfToInstance(rendersConf, featureSet)
	ret = collectAndBuildInstance(ret, rendersConf, featureSet)

	return ret
}

func (d *DefaultService) GetName() string {
	return "DefaultService"
}
