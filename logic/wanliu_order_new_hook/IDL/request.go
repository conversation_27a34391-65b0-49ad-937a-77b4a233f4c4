package IDL

import (
	"net/http"

	"git.xiaojukeji.com/falcon/go-biz/IDL"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/models/ddmq"
	"git.xiaojukeji.com/nuwa/binding"
)

type OrderNewTopic struct {
	*ddmq.BaseData
	Data TopicNewData `json:"data"`
}

type TopicNewData struct {
	BookingType   string       `json:"booking_type"`
	OrderId       string       `json:"order_id"`
	OrderFeature  string       `json:"order_feature"`
	DepartureTime string       `json:"departure_time"`
	ReplaceField  ReplaceField `json:"replace_field"`
}

type ReplaceField struct {
	OrderId string `json:"order_id"`
}

type rawRequest struct {
	Params       OrderNewTopic `json:"params"`
	CarreraTopic string        `protobuf:"bytes,2,opt,name=carrera_topic,json=carreraTopic,proto3" json:"carrera_topic"`
}

// PrivateParams 接口所需的私参
type ApiRequest struct {
	request rawRequest
}

// NewApiRequest 构造ApiRequest,不要自己直接初始化 ApiRequest,很多内部属性依赖构造函数
func NewApiRequest(req *http.Request) (*ApiRequest, error) {
	raw := rawRequest{}
	if err := binding.Form.Bind(req, &raw); err != nil {
		return nil, err
	}

	return &ApiRequest{request: raw}, nil
}

func (r *ApiRequest) GetLang() string {
	//todo
	return ""
}

func (r *ApiRequest) BookingType() IDL.BookingType {
	return IDL.BookingType(converter.StringToInt32(r.request.Params.Data.BookingType))
}

func (r *ApiRequest) OrderId() *IDL.OrderId {
	encdeOrderId := r.request.Params.Data.ReplaceField.OrderId
	if encdeOrderId == "" {
		encdeOrderId = r.request.Params.Data.OrderId
	}
	orderId, _ := IDL.NewFromEncodeId(encdeOrderId)
	return orderId
}

func (r *ApiRequest) OrderFeature() string {
	return r.request.Params.Data.OrderFeature
}

func (r *ApiRequest) DepartureTime() IDL.Time {
	time, _ := IDL.NewTimeFromString(r.request.Params.Data.DepartureTime)
	if time == nil {
		time = &IDL.Time{}
	}
	return *time
}
