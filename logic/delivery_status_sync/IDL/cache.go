package IDL

type DeliveryToOrderCache struct {
	// BaseDate
	DeliveryID          int64  `json:"delivery_id"`           // 运单ID（初始化写入）
	District            string `json:"district"`              // 订单区码（初始化）
	CountryCode         string `json:"country_code"`          // 国家码（初始化）
	Status              int64  `json:"status"`                // 当前状态（状态机状态码）
	SubStatus           int64  `json:"sub_status"`            // 当前子状态
	PrevStatus          int64  `json:"prev_status"`           // 上一个状态
	PrevSubStatus       int64  `json:"prev_sub_status"`       // 上一个子状态
	RetryMessageCounter int64  `json:"retry_message_counter"` // MQ消费-重试消息计数器

	// createOrderData
	EncodeOid       string `json:"encode_oid"`
	OrderID         int64  `json:"order_id"`          // 当前出行订单ID（创建订单）
	ReassignOrderID int64  `json:"reassign_order_id"` // 改派前出行订单ID（成单后改派写入）
	NewTime         int64  `json:"new_time"`          // 发单时间
	ProductId       int32  `json:"product_id"`

	// d_delivery_platform_order表中需要同步dos的数据
	DeliveryOrderData

	// 成单后数据
	AfterConfirmDate

	// dos的odyssey_delivery_info字段
	DeliveryInfo OdysseyDeliveryInfo `json:"odyssey_delivery_info"`

	needSyncCache        bool //是否需要更新cache
	confirmAfterReassign bool //成单后改派
}
type AfterConfirmDate struct {
	CarId         string `json:"car_id"`         // CarId(成单时写)
	DriverId      int64  `json:"driver_id"`      // 司机ID(成单时写)
	AssignTime    int64  `json:"assign_time"`    // 抢单时间
	PreparedTime  int64  `json:"prepared_time"`  // 到达时间
	BegunTime     int64  `json:"begun_time"`     // 送驾时间
	FinishedTime  int64  `json:"finished_time"`  // 完单时间
	CancelledTime int64  `json:"cancelled_time"` // 关单时间
}

// DeliveryOrderData d_delivery_platform_order表中需要同步dos的数据
type DeliveryOrderData struct {
	StartingName    string  `json:"starting_name" db:"from_address"` // B端商家地址
	StartingLng     float64 `json:"starting_lng" db:"from_lng"`      // B端商家经度
	StartingLat     float64 `json:"starting_lat" db:"from_lat"`      // B端商家精度
	DestName        string  `json:"dest_name" db:"to_address"`       // C端用户地址
	DestLng         float64 `json:"dest_lng" db:"to_lng"`            // C端用户经度
	DestLat         float64 `json:"dest_lat" db:"to_lat"`            // C端用户纬度
	PassengerUid    int64   `json:"passenger_uid" db:"uid"`          // C端用户UID
	OrderDataSynced bool    `json:"order_data_synced"`               // 是否同步order表数据
}

type OdysseyDeliveryInfo struct {
	DeliveryID   int64 `json:"delivery_id,omitempty"`   // 运单ID
	OrderID      int64 `json:"order_id,omitempty"`      // 外卖订单ID
	PassengerUid int64 `json:"passenger_uid,omitempty"` // C端用户UID
	DispatchTime int64 `json:"dispatch_time,omitempty"` // 引擎分单时间戳
	County       int32 `json:"county,omitempty"`        // 商家地区码
	ToCounty     int32 `json:"to_county,omitempty"`     // 目的地区码
}

func (c *DeliveryToOrderCache) IncrRetryMessageCounter() {
	c.RetryMessageCounter += 1
	c.needSyncCache = true
}

func (c *DeliveryToOrderCache) ClearRetryMessageCounter() {
	c.RetryMessageCounter = 0
	c.needSyncCache = true
}

func (c *DeliveryToOrderCache) NeedSyncCache() bool {
	return c.needSyncCache
}

func (c *DeliveryToOrderCache) SetNeedSyncCache() {
	c.needSyncCache = true
}

func (c *DeliveryToOrderCache) SetOrderDataSynced() {
	c.OrderDataSynced = true
	c.needSyncCache = true
}

func (c *DeliveryToOrderCache) GetOrderDataSynced() bool {
	return c.OrderDataSynced
}

func (c *DeliveryToOrderCache) ConfirmAfterReassign() bool {
	return c.confirmAfterReassign
}

func (c *DeliveryToOrderCache) SetConfirmAfterReassign() {
	c.confirmAfterReassign = true
}

type StatusSyncLock struct {
	Key   string `json:"key"`
	Value int64  `json:"value"`
}
