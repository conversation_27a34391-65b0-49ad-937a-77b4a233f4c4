package assign_order_operator

import (
	"context"
	"fmt"

	dosClient "git.xiaojukeji.com/falcon/go-biz/client/dos"
	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Operator{}

type Operator struct {
	framework.BaseComponent
}

func (o *Operator) GetName() string {
	return "assign_order_operator"
}

func (o *Operator) Run(ctx context.Context) error {
	if !o.needAssign(ctx) {
		return nil
	}
	assignOrderReq, err := o.buildAssignOrderReq(ctx)
	if err != nil || assignOrderReq == nil {
		zErr := zerr.New(consts.ErrnoAssignOrderFail).SetErrmsg(fmt.Sprintf("build_params,err=%v", err))
		return o.SetResponse(ctx, zErr, nil)
	}

	response, err := dosClient.GetSDKClient(ctx).AssignOrder(ctx, assignOrderReq)
	if err != nil {
		zErr := zerr.New(consts.ErrnoAssignOrderFail).SetErrmsg(fmt.Sprintf("client_err,err=%s", err.Error()))
		return o.SetResponse(ctx, zErr, nil)
	}
	if response.Errno != 0 {
		zErr := zerr.New(consts.ErrnoAssignOrderFail).SetErrmsg(fmt.Sprintf("resp_err,res=%s", converter.InterfaceToString(response)))
		return o.SetResponse(ctx, zErr, nil)
	}

	// 同步cache内容
	o.syncCache(ctx)
	return nil
}

func (o *Operator) needAssign(ctx context.Context) bool {
	process := o.Request().GetDeliveryProcess()
	if process.Status == consts.DeliveryStriveStatus && process.SubStatus != 0 {
		return true
	}
	return false
}

func (o *Operator) syncCache(ctx context.Context) {
	cache := o.BO().GetDeliveryToOrderCache()
	process := o.Request().GetDeliveryProcess()
	cache.AfterConfirmDate = IDL.AfterConfirmDate{
		CarId:      o.BO().VehicleDM().CarId(),
		DriverId:   process.CurrRiderID,
		AssignTime: process.UpdateTime,
	}

	cache.SetNeedSyncCache()
}
