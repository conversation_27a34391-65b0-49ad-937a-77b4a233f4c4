package update_order_operator

import (
	"context"

	dosSDK "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"
	bizIDL "git.xiaojukeji.com/falcon/go-biz/IDL"
	"git.xiaojukeji.com/falcon/go-biz/client/locsvr"
	"git.xiaojukeji.com/falcon/go-biz/utils/biz"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/IDL"
)

func (o *Operator) buildUpdateOrderReq(ctx context.Context) (*dosSDK.UpdateOrderReq, error) {
	cache := o.BO().GetDeliveryToOrderCache()
	updateOrderReq := &dosSDK.UpdateOrderReq{
		ProductId: converter.StringCopyPtr("0"),
		OrderId:   converter.StringCopyPtr(converter.Int64ToString(cache.OrderID)),
		District:  converter.StringCopyPtr(cache.District),
		WhereConditions: &dosSDK.WhereCondition{
			OrderId: converter.Int64CopyPtr(cache.OrderID),
		},
		OrderInfo:      dosSDK.NewUpdateOrderInfo(),
		OrderExtraInfo: dosSDK.NewUpdateOrderExtraInfo(),
	}

	switch o.Request().GetTable() {
	case IDL.DeliveryPlatformOrderTable:
		if err := o.addDeliverOrder(ctx, updateOrderReq); err != nil {
			return nil, err
		}
		break
	case IDL.DeliveryPlatformProcessTable:
		o.addDeliveryProcess(ctx, updateOrderReq)
	}

	return updateOrderReq, nil
}

func (o *Operator) addDeliverOrder(ctx context.Context, req *dosSDK.UpdateOrderReq) error {
	deliveryOrder := o.Request().GetDeliveryOrder()

	county, err := getCountyByLngLat(ctx, deliveryOrder.FromLat, deliveryOrder.FromLng)
	if err != nil {
		return err
	}
	toCounty, err := getCountyByLngLat(ctx, deliveryOrder.ToLat, deliveryOrder.ToLng)
	if err != nil {
		return err
	}

	req.OrderInfo.StartingName = converter.StringCopyPtr(deliveryOrder.FromAddress)
	req.OrderInfo.StartingLng = converter.Float64CopyPtr(deliveryOrder.FromLng)
	req.OrderInfo.StartingLat = converter.Float64CopyPtr(deliveryOrder.FromLat)
	req.OrderInfo.DestName = converter.StringCopyPtr(deliveryOrder.ToAddress)
	req.OrderInfo.DestLng = converter.Float64CopyPtr(deliveryOrder.ToLng)
	req.OrderInfo.DestLat = converter.Float64CopyPtr(deliveryOrder.ToLat)
	req.OrderInfo.County = converter.Int32CopyPtr(county)     //dos不支持Update接口更新County，只支持newOrder接口
	req.OrderInfo.ToCounty = converter.Int32CopyPtr(toCounty) //dos不支持Update接口更新ToCounty，只支持newOrder接口

	deliveryInfo := o.BO().GetDeliveryToOrderCache().DeliveryInfo
	deliveryInfo.PassengerUid = deliveryOrder.UID
	deliveryInfo.County = county
	deliveryInfo.ToCounty = toCounty
	req.OrderInfo.OdysseyDeliveryInfo = converter.StringCopyPtr(converter.InterfaceToString(deliveryInfo))

	return nil
}

func (o *Operator) addDeliveryProcess(ctx context.Context, req *dosSDK.UpdateOrderReq) {
	deliveryProcess := o.Request().GetDeliveryProcess()
	deliveryInfo := o.BO().GetDeliveryToOrderCache().DeliveryInfo
	deliveryInfo.DispatchTime = deliveryProcess.UpdateTime
	req.OrderInfo.OdysseyDeliveryInfo = converter.StringCopyPtr(converter.InterfaceToString(deliveryInfo))
}

func getCountyByLngLat(ctx context.Context, lat, lng float64) (int32, error) {
	request := biz.GetAreaInfoByLocRequest{
		Lat:        bizIDL.Latitude(lat),
		Lng:        bizIDL.Longitude(lng),
		Token:      locsvr.DriverToken,
		WithCounty: true,
		MapType:    bizIDL.MapTypeWgs84,
	}
	loc, err := biz.NewMapHelper(locsvr.GetClient(ctx)).GetAreaInfoByLoc(ctx, &request)
	if err != nil {
		return 0, err
	}
	return loc.Countyid, nil
}
