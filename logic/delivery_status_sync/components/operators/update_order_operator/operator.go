package update_order_operator

import (
	"context"
	"fmt"
	dosSDK "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"

	dosClient "git.xiaojukeji.com/falcon/go-biz/client/dos"
	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Operator{}

type Operator struct {
	framework.BaseComponent
}

func (o *Operator) GetName() string {
	return "update_order_operator"
}

func (o *Operator) Run(ctx context.Context) error {
	if !o.needUpdate(ctx) {
		return nil
	}

	updateOrderReq, err := o.buildUpdateOrderReq(ctx)
	if err != nil || updateOrderReq == nil {
		zErr := zerr.New(consts.ErrnoUpdateOrderFail).SetErrmsg(fmt.Sprintf("build_params,err=%v", err))
		return o.SetResponse(ctx, zErr, nil)
	}

	response, err := dosClient.GetSDKClient(ctx).UpdateOrder(ctx, updateOrderReq)
	if err != nil {
		zErr := zerr.New(consts.ErrnoUpdateOrderFail).SetErrmsg(fmt.Sprintf("dos_client_err,err=%s", err.Error()))
		return o.SetResponse(ctx, zErr, nil)
	}
	if response.Errno != 0 {
		zErr := zerr.New(consts.ErrnoUpdateOrderFail).SetErrmsg(fmt.Sprintf("dos_resp_err,res=%s", converter.InterfaceToString(response)))
		return o.SetResponse(ctx, zErr, nil)
	}

	o.syncCache(ctx, updateOrderReq)
	return nil
}

func (o *Operator) needUpdate(ctx context.Context) bool {
	switch o.Request().GetTable() {
	case IDL.DeliveryPlatformOrderTable:
		//同步 d_delivery_platform_order 表数据
		cache := o.BO().GetDeliveryToOrderCache()
		if !cache.OrderDataSynced {
			return true
		}
	case IDL.DeliveryPlatformProcessTable:
		//抢单动作
		process := o.Request().GetDeliveryProcess()
		if process.Status == consts.DeliveryStriveStatus && process.SubStatus == 0 {
			return true
		}
	}
	return false
}

func (o *Operator) syncCache(ctx context.Context, req *dosSDK.UpdateOrderReq) {
	cache := o.BO().GetDeliveryToOrderCache()
	switch o.Request().GetTable() {
	case IDL.DeliveryPlatformOrderTable:
		order := o.Request().GetDeliveryOrder()
		cache.DeliveryOrderData = IDL.DeliveryOrderData{
			StartingName: order.FromAddress,
			StartingLng:  order.FromLng,
			StartingLat:  order.FromLat,
			DestName:     order.ToAddress,
			DestLng:      order.ToLng,
			DestLat:      order.ToLat,
			PassengerUid: order.UID,
		}
		cache.DeliveryInfo.PassengerUid = order.UID
		cache.DeliveryInfo.County = *req.OrderInfo.County
		cache.DeliveryInfo.ToCounty = *req.OrderInfo.ToCounty
		cache.SetOrderDataSynced()

	case IDL.DeliveryPlatformProcessTable:
		//抢单动作
		process := o.Request().GetDeliveryProcess()
		cache.Status = process.Status
		cache.SubStatus = process.SubStatus
		cache.PrevStatus = process.PrevStatus
		cache.PrevSubStatus = process.PrevSubStatus
		cache.DeliveryInfo.DispatchTime = process.UpdateTime
		cache.SetNeedSyncCache()
	}
}
