package prepare_order_operator

import (
	"context"
	"fmt"

	dosClient "git.xiaojukeji.com/falcon/go-biz/client/dos"
	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Operator{}

type Operator struct {
	framework.BaseComponent
}

func (o *Operator) GetName() string {
	return "prepare_order_operator"
}

func (o *Operator) Run(ctx context.Context) error {
	prepareOrderReq, err := o.buildPrepareOrderReq(ctx)
	if err != nil || prepareOrderReq == nil {
		zErr := zerr.New(consts.ErrnoPrepareOrderFail).SetErrmsg(fmt.Sprintf("build_params,err=%v", err))
		return o.SetResponse(ctx, zErr, nil)
	}

	response, err := dosClient.GetSDKClient(ctx).PrepareOrder(ctx, prepareOrderReq)
	if err != nil {
		zErr := zerr.New(consts.ErrnoPrepareOrderFail).SetErrmsg(fmt.Sprintf("client_err,err=%s", err.Error()))
		return o.SetResponse(ctx, zErr, nil)
	}
	if response.Errno != 0 {
		zErr := zerr.New(consts.ErrnoPrepareOrderFail).SetErrmsg(fmt.Sprintf("resp_err,err=%s", converter.InterfaceToString(response)))
		return o.SetResponse(ctx, zErr, nil)
	}

	o.syncCache(ctx)
	return nil
}

func (o *Operator) syncCache(ctx context.Context) {
	cache := o.BO().GetDeliveryToOrderCache()
	process := o.Request().GetDeliveryProcess()
	cache.PreparedTime = process.UpdateTime
	cache.SetNeedSyncCache()
}
