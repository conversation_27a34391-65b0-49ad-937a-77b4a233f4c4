package finish_order_operator

import (
	"context"
	"fmt"

	dosClient "git.xiaojukeji.com/falcon/go-biz/client/dos"
	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/util"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Operator{}

type Operator struct {
	framework.BaseComponent
}

func (o *Operator) GetName() string {
	return "finish_order_operator"
}

func (o *Operator) Run(ctx context.Context) error {
	if !o.needCancel(ctx) {
		return nil
	}
	finishOrderReq, err := o.buildFinishOrderReq(ctx)
	if err != nil || finishOrderReq == nil {
		zErr := zerr.New(consts.ErrnoFinishOrderFail).SetErrmsg(fmt.Sprintf("build_params,err=%v", err))
		return o.SetResponse(ctx, zErr, nil)
	}

	response, err := dosClient.GetSDKClient(ctx).FinishOrder(ctx, finishOrderReq)
	if err != nil {
		zErr := zerr.New(consts.ErrnoFinishOrderFail).SetErrmsg(fmt.Sprintf("client_err,err=%s", err.Error()))
		return o.SetResponse(ctx, zErr, nil)
	}
	if response.Errno != 0 {
		zErr := zerr.New(consts.ErrnoFinishOrderFail).SetErrmsg(fmt.Sprintf("resp_err,err=%s", converter.InterfaceToString(response)))
		return o.SetResponse(ctx, zErr, nil)
	}

	o.syncCache(ctx)
	return nil
}

func (o *Operator) needCancel(ctx context.Context) bool {
	process := o.Request().GetDeliveryProcess()

	//关单 190-[3,4,10,11,12,13]场景
	if process.Status == consts.DeliveryAbortStatus && util.Int64InSlice(process.SubStatus, o.BO().GetFinishedAbortSubStatus()) {
		return true
	}

	// 正常完单
	if process.Status == consts.DeliveryFinishStatus {
		return true
	}

	return false
}

func (o *Operator) syncCache(ctx context.Context) {
	cache := o.BO().GetDeliveryToOrderCache()
	process := o.Request().GetDeliveryProcess()
	cache.FinishedTime = process.UpdateTime
	cache.SetNeedSyncCache()
}
