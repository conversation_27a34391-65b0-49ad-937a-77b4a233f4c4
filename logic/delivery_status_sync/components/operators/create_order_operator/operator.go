package create_order_operator

import (
	"context"
	"fmt"

	"git.xiaojukeji.com/falcon/fission-sdk/go/src/fissionclient"
	BizIDL "git.xiaojukeji.com/falcon/go-biz/IDL"
	dosClient "git.xiaojukeji.com/falcon/go-biz/client/dos"
	iceCline "git.xiaojukeji.com/falcon/go-biz/client/ice"
	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/go-biz/sdk/ice"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Operator{}

type Operator struct {
	framework.BaseComponent
}

func (o *Operator) GetName() string {
	return "create_order_operator"
}

func (o *Operator) Run(ctx context.Context) error {
	if !o.needCreate(ctx) {
		return nil
	}

	orderDate, err := o.createOrderId(ctx, o.BO().GetDeliveryToOrderCache().District)
	if err != nil {
		zErr := zerr.New(consts.ErrnoCreateOrderFail).SetErrmsg(fmt.Sprintf("create_order_id_err,err=%s", err.Error()))
		return o.SetResponse(ctx, zErr, nil)
	}

	newOrderInput, err := o.buildNewOrderInput(ctx, orderDate.GetLowOid())
	if err != nil || newOrderInput == nil {
		zErr := zerr.New(consts.ErrnoCreateOrderFail).SetErrmsg(fmt.Sprintf("build_params,err=%v", err))
		return o.SetResponse(ctx, zErr, nil)
	}
	response, err := dosClient.GetSDKClient(ctx).NewOrder(ctx, newOrderInput)
	if err != nil {
		zErr := zerr.New(consts.ErrnoCreateOrderFail).SetErrmsg(fmt.Sprintf("dos_client_err,err=%s", err.Error()))
		return o.SetResponse(ctx, zErr, nil)
	}
	if response.Errno != 0 {
		zErr := zerr.New(consts.ErrnoCreateOrderFail).SetErrmsg(fmt.Sprintf("dos_resp_err,err=%s", converter.InterfaceToString(response)))
		return o.SetResponse(ctx, zErr, nil)
	}

	o.syncCache(ctx, orderDate)
	return nil
}

func (o *Operator) needCreate(ctx context.Context) bool {
	cache := o.BO().GetDeliveryToOrderCache()
	process := o.Request().GetDeliveryProcess()
	// 初始化订单
	if cache.OrderID == 0 && process.Status == consts.DeliveryUnassignStatus &&
		process.PrevStatus == consts.DeliveryUnassignStatus {
		return true
	}

	// 成单后改派
	if cache.ConfirmAfterReassign() {
		return true
	}
	return false
}

func (o *Operator) syncCache(ctx context.Context, orderDate *BizIDL.OrderId) {
	cache := o.BO().GetDeliveryToOrderCache()
	process := o.Request().GetDeliveryProcess()

	if cache.OrderID != 0 {
		cache.ReassignOrderID = cache.OrderID
		cache.DeliveryInfo.DispatchTime = 0
	}
	cache.OrderID = orderDate.GetLowOid()
	cache.EncodeOid = orderDate.GetEncodeOid()
	cache.NewTime = process.UpdateTime
	cache.ProductId = fissionclient.GetProductIdByFoodDeliveryModel(cache.CountryCode, int32(process.DeliveryModel))
	cache.AfterConfirmDate = IDL.AfterConfirmDate{}
}

func (o *Operator) createOrderId(ctx context.Context, districtCode string) (*BizIDL.OrderId, error) {
	// 创建新订单id
	resp, err := iceCline.GetClient(ctx).GetOrderId(ctx, &ice.GetOrderIdReq{
		Product: consts.IceDefaultProduct,
	})
	if err != nil || resp == nil || resp.Errno != 0 || resp.Id <= 0 {
		return nil, fmt.Errorf("get order id failed, err: %v", err)
	}

	orderIdData, err := BizIDL.NewOrderId(resp.Id, districtCode)
	if err != nil {
		return nil, fmt.Errorf("create order id failed, err: %v", err)
	}

	return orderIdData, nil
}
