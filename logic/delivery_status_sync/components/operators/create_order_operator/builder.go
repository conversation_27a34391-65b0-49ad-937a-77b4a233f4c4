package create_order_operator

import (
	"context"

	"git.xiaojukeji.com/Elvish/elvish-lib-golang/datetimeutil"
	dosSDK "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"
	"git.xiaojukeji.com/falcon/fission-sdk/go/src/fissionclient"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/IDL"
)

func (o *Operator) buildNewOrderInput(ctx context.Context, orderId int64) (*dosSDK.NewOrderReq, error) {
	newOrderInput := dosSDK.NewNewOrderReq()
	newOrderInput.ProductId = converter.StringCopyPtr("0")
	orderExtra := dosSDK.NewNewOrderExtraInfo()

	cache := o.BO().GetDeliveryToOrderCache()
	deliveryProcess := o.Request().GetDeliveryProcess()

	productId := fissionclient.GetProductIdByFoodDeliveryModel(cache.CountryCode, int32(deliveryProcess.DeliveryModel))
	creatTime, err := datetimeutil.FormatBeijing(deliveryProcess.UpdateTime)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoElvishError, "creatTime FormatBeijing error")
	}
	newOrderInfo := &dosSDK.NewOrderInfoV1{
		OrderId:             orderId,
		PassengerId:         87961739735916, //TODO mock 一个值
		District:            converter.StringCopyPtr(cache.District),
		RequireLevel:        converter.StringCopyPtr(converter.Int32ToString(productId)),
		ProductId:           converter.Int32CopyPtr(productId),
		BusinessId:          converter.Int32CopyPtr(productId),
		CountryIsoCode:      converter.StringCopyPtr(cache.CountryCode),
		Area:                int32(deliveryProcess.CityID),
		ToArea:              converter.Int32CopyPtr(int32(deliveryProcess.CityID)), //没有垮成单,写死city_id
		ComboType:           converter.Int32CopyPtr(0),
		Type:                0,
		Channel:             o.BO().GetChannel(deliveryProcess.Channel),
		CreateTime:          converter.StringCopyPtr(creatTime),
		DepartureTime:       converter.StringCopyPtr(creatTime),
		OdysseyDeliveryInfo: converter.StringCopyPtr(converter.InterfaceToString(cache.DeliveryInfo)),
	}

	// 成单后改派场景
	if cache.ConfirmAfterReassign() {
		o.fillReassignDate(newOrderInfo, cache)
		orderExtra.ReassignLastOrderId = dosSDK.StrPtr(converter.Int64ToString(cache.OrderID))
	}

	newOrderInput.OrderInfo = newOrderInfo
	newOrderInput.OrderExtra = orderExtra
	return newOrderInput, nil
}

func (o *Operator) fillReassignDate(newOrderInfo *dosSDK.NewOrderInfoV1, cache *IDL.DeliveryToOrderCache) {
	if !cache.GetOrderDataSynced() {
		return
	}
	newOrderInfo.StartingName = converter.StringCopyPtr(cache.StartingName)
	newOrderInfo.StartingLng = converter.Float64CopyPtr(cache.StartingLng)
	newOrderInfo.StartingLat = converter.Float64CopyPtr(cache.StartingLat)
	newOrderInfo.DestName = converter.StringCopyPtr(cache.DestName)
	newOrderInfo.DestLng = converter.Float64CopyPtr(cache.DestLng)
	newOrderInfo.DestLat = converter.Float64CopyPtr(cache.DestLat)

	deliveryInfo := IDL.OdysseyDeliveryInfo{
		DeliveryID:   cache.DeliveryID,
		OrderID:      cache.OrderID,
		PassengerUid: cache.PassengerUid,
	}
	newOrderInfo.OdysseyDeliveryInfo = converter.StringCopyPtr(converter.InterfaceToString(deliveryInfo))
}
