package driver_loader

import (
	"context"
	"fmt"

	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/go-biz/domain_model/driver"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Loader{}

type Loader struct {
	framework.BaseComponent
}

func (l *Loader) GetName() string {
	return "driver_loader"
}

func (l *Loader) Run(ctx context.Context) error {
	if l.Request().GetTable() != IDL.DeliveryPlatformProcessTable {
		return l.SetSuccessResponse(ctx)
	}
	dm, err := driver.CreateFromDriverId(ctx, l.Request().GetDeliveryProcess().CurrRiderID)
	if err != nil || dm == nil {
		zErr := zerr.New(consts.ErrnoUranusError).SetErrmsg(fmt.Sprintf("CreateFromDriverId failed,err=%v", err))
		return l.SetResponse(ctx, zErr, nil)
	}

	vehicleDM, err := dm.ForceGetOnlineVehicleDM()
	if err != nil || vehicleDM == nil {
		zErr := zerr.New(consts.ErrnoUranusError).SetErrmsg(fmt.Sprintf("GetDriverVehicleDM failed with,err=%v", err))
		return l.SetResponse(ctx, zErr, nil)
	}

	l.BO().SetDriverDM(dm)
	l.BO().SetVehicleDM(vehicleDM)
	return nil
}
