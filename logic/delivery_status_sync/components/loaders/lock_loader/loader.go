package lock_loader

import (
	"context"
	"fmt"
	"time"

	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/redis"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Loader{}

type Loader struct {
	framework.BaseComponent
}

func (l *Loader) GetName() string {
	return "lock_loader"
}

func (l *Loader) Run(ctx context.Context) error {
	key := fmt.Sprintf(consts.DeliverStatusSyncLock, converter.Int64ToString(l.Request().GetDeliveryID()))
	lockVal := time.Now().Unix()

	result, err := redis.StoreConnection.SetNEx(ctx, key, consts.DeliverStatusSyncLockExpireTime, lockVal)
	if err != nil {
		zErr := zerr.New(consts.ErrnoSetRedisError).SetErrmsg(fmt.Sprintf("SetNEx_failed,key=%s,val=%d,res=%s,err=%v", key, lockVal, result, err))
		return l.SetResponse(ctx, zErr, nil)
	}

	syncLock := &IDL.StatusSyncLock{
		Key:   key,
		Value: lockVal,
	}

	l.BO().SetStatusSyncLock(syncLock)
	return nil
}
