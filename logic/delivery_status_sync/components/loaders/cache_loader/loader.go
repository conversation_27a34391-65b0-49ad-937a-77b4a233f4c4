package cache_loader

import (
	"context"
	"encoding/json"
	"fmt"

	"git.xiaojukeji.com/falcon/go-biz/client/locsvr"
	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/redis"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Loader{}

type Loader struct {
	framework.BaseComponent
}

func (l *Loader) GetName() string {
	return "cache_loader"
}

func (l *Loader) Run(ctx context.Context) error {
	key := fmt.Sprintf(consts.DeliveryToOrderCacheKey, converter.Int64ToString(l.Request().GetDeliveryID()))
	exists, err := redis.StoreConnection.Exists(ctx, key)
	if err != nil {
		zErr := zerr.New(consts.ErrnoRedisError).SetErrmsg(fmt.Sprintf("%s,exists_failed,err=%s", l.GetName(), err.Error()))
		return l.SetResponse(ctx, zErr, nil)
	}

	// 查看是否是合法场景（110 -> 110）
	if !exists {
		return l.checkDeliveryStatus(ctx)
	}

	cacheData, err := redis.StoreConnection.Get(ctx, key)
	if err != nil {
		zErr := zerr.New(consts.ErrnoRedisError).SetErrmsg(fmt.Sprintf("%s,get_failed,err=%s", l.GetName(), err.Error()))
		return l.SetResponse(ctx, zErr, nil)
	}

	cache := new(IDL.DeliveryToOrderCache)
	if err = json.Unmarshal([]byte(cacheData), cache); err != nil {
		zErr := zerr.New(consts.ErrnoUnmarshalError).SetErrmsg(fmt.Sprintf("cache_unmarshal_Err,err=%s", err.Error()))
		return l.SetResponse(ctx, zErr, nil)
	}

	log.Infof(ctx, l.GetName(), "delivery_id=%d||key=%s||cache=%s", l.Request().GetDeliveryID(), key, cacheData)
	l.BO().SetDeliveryToOrderCache(cache)
	return nil
}

func (l *Loader) checkDeliveryStatus(ctx context.Context) error {
	// 110->110 新订单场景是没有缓存的，需要手动写入
	if l.Request().GetTable() == IDL.DeliveryPlatformProcessTable {
		DeliveryProcess := l.Request().GetDeliveryProcess()
		if DeliveryProcess.Status != consts.DeliveryUnassignStatus || DeliveryProcess.PrevStatus != consts.DeliveryUnassignStatus {
			// 配置监控报警，需要重试
			return l.SetResponse(ctx, zerr.New(consts.ErrnoCreateMsgLate), nil)
		}

		// 初始化缓存
		if err := l.cacheInit(ctx); err != nil {
			zErr := zerr.New(consts.ErrnoParamsError).SetErrmsg(fmt.Sprintf("cache_init_faild,err=%s", err.Error()))
			return l.SetResponse(ctx, zErr, nil)
		}

	} else if l.Request().GetTable() == IDL.DeliveryPlatformOrderTable {
		// order表动作先来了，需要重试
		zErr := zerr.New(consts.ErrnoCreateMsgLate).SetErrmsg("late creation of order message")
		return l.SetResponse(ctx, zErr, nil)
	}
	return nil
}

func (l *Loader) cacheInit(ctx context.Context) error {
	DeliveryProcess := l.Request().GetDeliveryProcess()

	cityInfo, err := locsvr.GetHttpClient(ctx).GetCityInfoByCityId(ctx, int32(DeliveryProcess.CityID))
	if err != nil {
		return err
	}

	cache := &IDL.DeliveryToOrderCache{
		DeliveryID:  DeliveryProcess.DeliveryID,
		Status:      DeliveryProcess.Status,
		SubStatus:   DeliveryProcess.SubStatus,
		PrevStatus:  DeliveryProcess.PrevStatus,
		District:    cityInfo.DistrictCode,
		CountryCode: cityInfo.CanonicalCountryCode,
		DeliveryInfo: IDL.OdysseyDeliveryInfo{
			DeliveryID: DeliveryProcess.DeliveryID,
			OrderID:     DeliveryProcess.OrderID,
		},
	}

	l.BO().SetDeliveryToOrderCache(cache)
	return nil
}
