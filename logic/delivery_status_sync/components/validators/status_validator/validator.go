package status_validator

import (
	"context"

	"git.xiaojukeji.com/falcon/go-biz/components/zerr"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/util"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/delivery_status_sync/framework"
	"git.xiaojukeji.com/falcon/zarya-core/frameworkV2/components"
)

var _ components.ComponentInterface = &Validator{}

// 130,140,150,160,190 节点增加 （当前司机ID == 新消息的司机ID） 条件
var driverIdCheckStatus = []int64{consts.DeliveryArrivedStartStatus, consts.DeliveryTakeStatus,
	consts.DeliveryArrivedDestStatus, consts.DeliveryFinishStatus}

type Validator struct {
	framework.BaseComponent
}

func (v *Validator) GetName() string {
	return "status_validator"
}

func (v *Validator) Run(ctx context.Context) error {
	deliveryToOrderCache := v.BO().GetDeliveryToOrderCache()

	if v.Request().GetTable() == IDL.DeliveryPlatformOrderTable {
		if deliveryToOrderCache.GetOrderDataSynced() {
			return v.SetSuccessResponse(ctx)
		}
	}
	if v.Request().GetTable() == IDL.DeliveryPlatformProcessTable {
		deliveryProcess := v.Request().GetDeliveryProcess()
		if deliveryToOrderCache.Status != deliveryProcess.PrevStatus || deliveryToOrderCache.SubStatus != deliveryProcess.PrevSubStatus {
			// 120-0 -> 120-1/2 只修改了subStatus，没有同步到prev
			if deliveryToOrderCache.PrevStatus == deliveryProcess.PrevStatus {
				if deliveryToOrderCache.Status == consts.DeliveryStriveStatus && deliveryProcess.Status == consts.DeliveryStriveStatus {
					if deliveryToOrderCache.SubStatus < deliveryProcess.SubStatus {
						return nil
					}
				}
			}
			deliveryToOrderCache.IncrRetryMessageCounter()
			log.Warnf(ctx, consts.ErrnoDeliveryStatusNotMatch, "delivery_status_sync_retry_count,delivery_id=%d,retry_message_counter=%d",
				deliveryProcess.DeliveryID, deliveryToOrderCache.RetryMessageCounter)
			return v.SetResponse(ctx, zerr.New(consts.ErrnoDeliveryStatusNotMatch), nil)
		}

		// 190场景需要看是否是成单
		if util.Int64InSlice(deliveryProcess.Status, driverIdCheckStatus) ||
			(deliveryProcess.Status == consts.DeliveryAbortStatus && deliveryToOrderCache.DriverId != 0) {
			if deliveryToOrderCache.DriverId != deliveryProcess.CurrRiderID {
				return v.SetResponse(ctx, zerr.New(consts.ErrnoDeliveryDriverNotMatch), nil)
			}
		}

		// 是否是成单后改派
		if deliveryToOrderCache.OrderID != 0 && deliveryToOrderCache.DriverId != 0 &&
			util.Int64InSlice(deliveryProcess.PrevStatus, v.BO().GetReassignStatus()) &&
			util.Int64InSlice(deliveryProcess.Status, v.BO().GetReassignAfterStatus()) {
			deliveryToOrderCache.SetConfirmAfterReassign()
		}
	}
	return nil
}
