package wanliu_order_reload_finished_hook

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.xiaojukeji.com/falcon/go-biz/client/locsvr"
	linkConsts "git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/jw_order/consts"

	UniGWIDL "git.xiaojukeji.com/cc/uni-gateway-golang-sdk/v3/idl"
	_ "git.xiaojukeji.com/dirpc/dirpc-go-LocsvrThrift"
	SDK "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Locsvr"
	IDL2 "git.xiaojukeji.com/falcon/go-biz/IDL"
	"git.xiaojukeji.com/falcon/go-biz/utils/biz"
	"git.xiaojukeji.com/falcon/go-biz/utils/http"
	IMCIDL "git.xiaojukeji.com/falcon/imc-go-sdk/idl"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/jw_order/wanliu_order_reload_finished_hook/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_reload_finished_hook/framework"
	"git.xiaojukeji.com/falcon/zarya-async-api/models/rpc/imc"
)

const (
	jwTaxiTerminalID = 300320 // JW打车终端ID

	// 默认配置，当 Apollo 配置不可用时使用
	ModuleKey = "wanliu_order_reload_finished_hook"
)

type FeatureComponent_JWTaxiIMC struct {
	framework.BaseComponent
	FeatureBO *IDL.BusinessInfo
}

func (c *FeatureComponent_JWTaxiIMC) Run(ctx context.Context) error {
	// 检查乘客终端ID是否为JW打车
	orderDM := c.BO().OrderDM()
	if orderDM == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "order domain model is nil")
		return nil
	}

	// 从订单领域模型获取终端ID
	terminalID := orderDM.PassengerTerminalId()
	if terminalID != jwTaxiTerminalID {
		return nil
	}

	// 检查是否为国际订单（如果查不到就是国内订单）
	areaInfo, err := c.loadStartingAreaInfo(ctx, orderDM.NewLat(), orderDM.NewLng())
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "load starting area info error, err=%v", err)
		return nil
	}

	if areaInfo == nil {
		return nil
	}

	if areaInfo != nil && *areaInfo.CanonicalCountryCode != orderDM.CountryIsoCode() {
		log.Infof(ctx, "skip_international_order", "skip international order, location=%s, country=%s",
			*areaInfo.CanonicalCountryCode, orderDM.CountryIsoCode())
		return nil
	}

	// 检查是否为司机取消订单场景
	if c.Request().NewOrder() != nil && c.Request().NewOrder().OrderId() == 0 {
		// 发送IMC推送通知
		err = c.sendImcToPassenger(ctx)
		if err != nil {
			log.Warnf(ctx, consts.ErrnoIMCerror, "send imc error, err=%v", err)
		}
	}

	return nil
}

func (c *FeatureComponent_JWTaxiIMC) sendImcToPassenger(ctx context.Context) error {
	orderDM := c.BO().OrderDM()
	var hintContent http.HintContent
	err := json.Unmarshal([]byte(orderDM.HintContent()), &hintContent)
	if err != nil {
		return err
	}

	lang := hintContent.Lang
	countryCode := orderDM.CountryCode()
	uid := biz.GetUidFromPid(orderDM.PassengerId())

	// 创建模板参数（司机取消订单场景不需要额外参数）
	templateParams := map[string]string{}

	// 创建跳转参数
	paramList := map[string]string{
		"url": fmt.Sprintf(linkConsts.PushLinkTemplate, orderDM.OrderId(), orderDM.ProductId()),
	}

	// 发送端外Push通知
	err = c.sendPushNotification(ctx, uid, lang, countryCode, templateParams, paramList)
	if err != nil {
		return err
	}
	/*
		// 短信的跳转链接与端外Push不同
		smsParamList := map[string]string{
			"url": linkConsts.SmsLinkMock, // 短信跳转链接
		}

		// 发送短信通知
		err = c.sendSmsNotification(ctx, uid, lang, countryCode, templateParams, smsParamList)
		if err != nil {
			log.Warnf(ctx, consts.ErrnoIMCerror, "send sms notification error, err=%v", err)
		}
	*/
	return nil
}

// 发送端外Push通知
func (c *FeatureComponent_JWTaxiIMC) sendPushNotification(ctx context.Context, uid int64, lang, countryCode string,
	templateParams map[string]string, paramList map[string]string) error {
	imcConfig := c.BO().GetModuleTypeConfig(ModuleKey, "push")
	if imcConfig == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "imc config is nil")
		return fmt.Errorf("imc config is nil")
	}

	req := &IMCIDL.TemplateMessageReq{
		Control: &IMCIDL.Control{},
		TmplMsg: &IMCIDL.TmplMessage{
			TemplateId: imcConfig.Key, // 端外Push模板ID
			Lang:       lang,
			Country:    countryCode,
			CombinedUsers: []*UniGWIDL.CombinedUser{{
				Uid: uid,
			}},
			TemplateParams: templateParams,
			FuncParams: &IMCIDL.FuncParams{
				LinkTracking: &IMCIDL.LinkTracking{
					ParamList: paramList,
				},
			},
			AppType: consts.AppTypeDomesticPassenger,
		},
	}
	config := imc.IMCConfigConfig{
		SecretKey: imcConfig.SecretKey,
		Bid:       imcConfig.Bid,
	}
	_, err := imc.GetClient().PushMessageV2WithConfig(ctx, req, imc.IMCConfigWanliuOrderStrived, config)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "send push notification error, err=%v", err)
	}
	return err
}

// 发送短信通知
func (c *FeatureComponent_JWTaxiIMC) sendSmsNotification(ctx context.Context, uid int64, lang, countryCode string,
	templateParams map[string]string, paramList map[string]string) error {

	// 从 Apollo 获取配置
	imcConfig := c.BO().GetModuleTypeConfig(ModuleKey, "sms")
	if imcConfig == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "imc config is nil")
		return fmt.Errorf("imc config is nil")
	}

	req := &IMCIDL.TemplateMessageReq{
		Control: &IMCIDL.Control{},
		TmplMsg: &IMCIDL.TmplMessage{
			TemplateId: imcConfig.Key, // 短信模板ID
			Lang:       lang,
			Country:    countryCode,
			CombinedUsers: []*UniGWIDL.CombinedUser{{
				Uid: uid,
			}},
			TemplateParams: templateParams,
			FuncParams: &IMCIDL.FuncParams{
				LinkTracking: &IMCIDL.LinkTracking{
					ParamList: paramList,
				},
			},
			AppType: consts.AppTypeDomesticPassenger,
		},
	}
	config := imc.IMCConfigConfig{
		SecretKey: imcConfig.SecretKey,
		Bid:       imcConfig.Bid,
	}
	_, err := imc.GetClient().PushMessageV2WithConfig(ctx, req, imc.IMCConfigWanliuOrderStrived, config)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "send sms notification error, err=%v", err)
	}
	return err
}

// 加载订单的起点城市信息
func (c *FeatureComponent_JWTaxiIMC) loadStartingAreaInfo(ctx context.Context, lat IDL2.Latitude, lng IDL2.Longitude) (*SDK.Cityinfo, error) {
	client := locsvr.GetClient(ctx)
	if client == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "loc svr client init failed")
		return nil, errors.New("loc svr client init failed")
	}
	localHelper := biz.NewMapHelper(client)
	request := biz.GetAreaInfoByLocRequest{
		Lat:        lat,
		Lng:        lng,
		Token:      "D3A19B7FAA8BBE830970E671924A5EFA",
		WithCounty: true,
		MapType:    "wgs84",
	}
	locRes, err1 := localHelper.GetAreaInfoByLoc(ctx, &request)
	if errors.Is(err1, biz.ErrorCityInfoEmpty) {
		return nil, nil
	}
	if err1 != nil {
		return nil, err1
	}
	return locRes, nil
}
