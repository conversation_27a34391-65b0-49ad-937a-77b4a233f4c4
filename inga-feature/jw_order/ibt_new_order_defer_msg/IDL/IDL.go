package IDL

import (
	"git.xiaojukeji.com/falcon/go-biz/domain_model/order"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/ibt_new_order_defer_msg/IDL/base_object"
)

type BusinessInfo struct {
	base_object.BaseFeatureBusinessObject
	orderDM *order.DomainModel
}

// SetOrderDM 设置订单领域模型
func (b *BusinessInfo) SetOrderDM(dm *order.DomainModel) {
	b.orderDM = dm
}

// OrderDM 获取订单领域模型
func (b *BusinessInfo) OrderDM() *order.DomainModel {
	return b.orderDM
}
