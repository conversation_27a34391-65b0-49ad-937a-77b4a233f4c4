package wanliu_order_strived_hook

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.xiaojukeji.com/falcon/go-biz/client/locsvr"
	linkConsts "git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/jw_order/consts"

	"git.xiaojukeji.com/Elvish/elvish-lib-golang/datetimeutil"
	"git.xiaojukeji.com/Elvish/elvish-lib-golang/measureutil"
	UniGWIDL "git.xiaojukeji.com/cc/uni-gateway-golang-sdk/v3/idl"
	_ "git.xiaojukeji.com/dirpc/dirpc-go-LocsvrThrift"
	SDK "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Locsvr"
	IDL2 "git.xiaojukeji.com/falcon/go-biz/IDL"
	"git.xiaojukeji.com/falcon/go-biz/utils/biz"
	"git.xiaojukeji.com/falcon/go-biz/utils/http"
	IMCIDL "git.xiaojukeji.com/falcon/imc-go-sdk/idl"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/jw_order/wanliu_order_strived_hook/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_strived_hook/framework"
	"git.xiaojukeji.com/falcon/zarya-async-api/models/rpc/imc"
)

const (
	jwTaxiTerminalID = 300320 // JW打车终端ID
	ModuleName       = "wanliu_order_strived_hook"
)

type FeatureComponent_JWTaxiIMC struct {
	framework.BaseComponent
	FeatureBO *IDL.BusinessInfo
}

func (c *FeatureComponent_JWTaxiIMC) Run(ctx context.Context) error {

	orderDM := c.BO().OrderDM()
	if orderDM == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "order domain model is nil")
		return nil
	}

	// 检查乘客终端ID是否为JW打车
	terminalID := orderDM.PassengerTerminalId()
	if terminalID != jwTaxiTerminalID {
		return nil
	}

	// 检查是否为国际订单（如果查不到就是国内订单）
	areaInfo, err := c.loadStartingAreaInfo(ctx, orderDM.NewLat(), orderDM.NewLng())
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "load starting area info error, err=%v", err)
		return nil
	}
	if areaInfo == nil {
		return nil
	}
	if areaInfo != nil && *areaInfo.CanonicalCountryCode != orderDM.CountryIsoCode() {
		log.Infof(ctx, "skip_international_order", "skip international order, location=%s, country=%s",
			*areaInfo.CanonicalCountryCode, orderDM.CountryIsoCode())
		return nil
	}

	// 发送IMC推送通知
	err = c.sendImcToPassenger(ctx)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "send imc error, err=%v", err)
		return nil
	}
	return nil
}

func (c *FeatureComponent_JWTaxiIMC) sendImcToPassenger(ctx context.Context) error {
	orderDM := c.BO().OrderDM()
	var hintContent http.HintContent
	err := json.Unmarshal([]byte(orderDM.HintContent()), &hintContent)
	if err != nil {
		return err
	}

	lang := hintContent.Lang
	countryCode := orderDM.CountryCode()
	uid := biz.GetUidFromPid(orderDM.PassengerId())

	// 从请求中获取ETA和EDA
	eta := c.Request().Eta()
	eda := c.Request().Eda()

	// 格式化ETA和EDA
	etaMinutes := c.formatETA(ctx, eta, lang)
	edaKm := c.formatEDA(ctx, eda, hintContent.Locale)

	// 创建模板参数
	templateParams := map[string]string{
		"starting_name":     orderDM.FromName(),
		"dist":              edaKm,
		"remaining_minutes": etaMinutes,
	}

	// 检查模板参数是否为空
	if orderDM.FromName() == "" || edaKm == "" || etaMinutes == "" {
		log.Warnf(ctx, consts.ErrnoIMCerror, "plateNo or edaKm or etaMinutes is empty, from_name=%s, edaKm=%s, etaMinutes=%s",
			orderDM.FromName(), edaKm, etaMinutes)
		return nil
	}

	// 创建跳转参数
	paramList := map[string]string{
		"url": fmt.Sprintf(linkConsts.PushLinkTemplate, orderDM.OrderId().GetEncodeOid(), orderDM.ProductId()),
	}

	// 发送端外Push通知
	pushErr := c.sendPushNotification(ctx, uid, lang, countryCode, templateParams, paramList)
	/*
		// 短信的跳转链接与端外Push不同
		smsParamList := map[string]string{
			"url": linkConsts.SmsLinkMock, // 短信跳转链接
		}

		// 发送短信通知
		smsErr := c.sendSmsNotification(ctx, uid, lang, countryCode, templateParams, smsParamList)

		// 如果两种通知都失败，返回错误
		if pushErr != nil && smsErr != nil {
			log.Warnf(ctx, consts.ErrnoIMCerror, "both push and sms failed, push_err=%v, sms_err=%v", pushErr, smsErr)
			return pushErr // 返回端外Push的错误
		}

	*/
	if pushErr != nil {
		return pushErr
	}
	return nil
}

// 发送端外Push通知
func (c *FeatureComponent_JWTaxiIMC) sendPushNotification(ctx context.Context, uid int64, lang, countryCode string,
	templateParams map[string]string, paramList map[string]string) error {
	imcConfig := c.BO().GetModuleTypeConfig(ModuleName, "push")

	if imcConfig == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "imc config is nil")
		return fmt.Errorf("imc config is nil")
	}
	req := &IMCIDL.TemplateMessageReq{
		Control: &IMCIDL.Control{},
		TmplMsg: &IMCIDL.TmplMessage{
			TemplateId: imcConfig.Key, // 端外Push模板ID
			Lang:       lang,
			Country:    countryCode,
			CombinedUsers: []*UniGWIDL.CombinedUser{{
				Uid: uid,
			}},
			TemplateParams: templateParams,
			FuncParams: &IMCIDL.FuncParams{
				LinkTracking: &IMCIDL.LinkTracking{
					ParamList: paramList,
				},
			},
			AppType: consts.AppTypeDomesticPassenger,
		},
	}
	config := imc.IMCConfigConfig{
		SecretKey: imcConfig.SecretKey,
		Bid:       imcConfig.Bid,
	}
	// 使用新的IMC配置发送消息
	_, err := imc.GetClientWithConfig(imc.IMCConfigWanliuOrderStrived).PushMessageV2WithConfig(ctx, req, imc.IMCConfigWanliuOrderStrived, config)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "send push notification error, err=%v", err)
	}
	return err
}

// 发送短信通知
func (c *FeatureComponent_JWTaxiIMC) sendSmsNotification(ctx context.Context, uid int64, lang, countryCode string,
	templateParams map[string]string, paramList map[string]string) error {
	imcConfig := c.BO().GetModuleTypeConfig(ModuleName, "sms")

	if imcConfig == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "imc config is nil")
		return fmt.Errorf("imc config is nil")
	}
	req := &IMCIDL.TemplateMessageReq{
		Control: &IMCIDL.Control{},
		TmplMsg: &IMCIDL.TmplMessage{
			TemplateId: imcConfig.Key, // 短信模板ID
			Lang:       lang,
			Country:    countryCode,
			CombinedUsers: []*UniGWIDL.CombinedUser{{
				Uid: uid,
			}},
			TemplateParams: templateParams,
			FuncParams: &IMCIDL.FuncParams{
				LinkTracking: &IMCIDL.LinkTracking{
					ParamList: paramList,
				},
			},
			AppType: consts.AppTypeDomesticPassenger,
		},
	}
	config := imc.IMCConfigConfig{
		SecretKey: imcConfig.SecretKey,
		Bid:       imcConfig.Bid,
	}
	// 使用新的IMC配置发送消息
	_, err := imc.GetClientWithConfig(imc.IMCConfigWanliuOrderStrived).PushMessageV2WithConfig(ctx, req, imc.IMCConfigWanliuOrderStrived, config)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "send sms notification error, err=%v", err)
	}
	return err
}

// 格式化ETA（预计到达时间）为分钟
func (c *FeatureComponent_JWTaxiIMC) formatETA(ctx context.Context, eta int, lang string) string {
	// 如果ETA无效，返回空字符串
	if eta == 0 {
		return ""
	}

	// 将秒转换为分钟（向上取整）
	minutes := (eta + 59) / 60

	var hintContent http.HintContent
	err := json.Unmarshal([]byte(c.BO().OrderDM().HintContent()), &hintContent)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoElvishError, "unmarshal hint content error, err=%v", err)
		return fmt.Sprintf("%d", minutes)
	}

	// 使用Elvish格式化时间
	formattedTime, err := datetimeutil.FormatDuration(hintContent.Locale, int64(minutes*60))
	if err != nil {
		log.Warnf(ctx, consts.ErrnoElvishError, "format duration error, err=%v", err)
		return fmt.Sprintf("%d", minutes)
	}

	return formattedTime
}

// 格式化EDA（预计到达距离）为公里
func (c *FeatureComponent_JWTaxiIMC) formatEDA(ctx context.Context, eda int, locale string) string {
	// 如果EDA无效或locale为空，返回空字符串
	if eda == 0 || locale == "" {
		return ""
	}

	// 创建距离参数
	distanceParam := measureutil.DistanceParam{
		Locale:    locale,
		Meter:     float64(eda),
		Unit:      measureutil.DISTANCE_KILOMETER,
		Highlight: false,
	}

	// 使用Elvish格式化距离
	distance, err := measureutil.FormatDistance(distanceParam)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoElvishError, "format distance error, param=%+v, err=%v", distanceParam, err)
		// 格式化失败时返回空字符串
		return ""
	}

	return distance
}

// 加载订单的起点城市信息
func (c *FeatureComponent_JWTaxiIMC) loadStartingAreaInfo(ctx context.Context, lat IDL2.Latitude, lng IDL2.Longitude) (*SDK.Cityinfo, error) {
	client := locsvr.GetClient(ctx)
	if client == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "loc svr client init failed")
		return nil, errors.New("loc svr client init failed")
	}
	localHelper := biz.NewMapHelper(client)
	request := biz.GetAreaInfoByLocRequest{
		Lat:        lat,
		Lng:        lng,
		Token:      "D3A19B7FAA8BBE830970E671924A5EFA",
		WithCounty: true,
		MapType:    "wgs84",
	}
	locRes, err1 := localHelper.GetAreaInfoByLoc(ctx, &request)
	if errors.Is(err1, biz.ErrorCityInfoEmpty) {
		return nil, nil
	}
	if err1 != nil {
		return nil, err1
	}
	return locRes, nil
}
