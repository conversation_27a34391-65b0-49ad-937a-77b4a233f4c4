package wanliu_order_strived_hook

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.xiaojukeji.com/falcon/go-biz/client/locsvr"

	UniGWIDL "git.xiaojukeji.com/cc/uni-gateway-golang-sdk/v3/idl"
	_ "git.xiaojukeji.com/dirpc/dirpc-go-LocsvrThrift"
	uniqueSDK "git.xiaojukeji.com/dirpc/dirpc-go-http-Unique"
	SDK "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Locsvr"
	IDL2 "git.xiaojukeji.com/falcon/go-biz/IDL"
	"git.xiaojukeji.com/falcon/go-biz/domain_model/order"
	"git.xiaojukeji.com/falcon/go-biz/utils/biz"
	"git.xiaojukeji.com/falcon/go-biz/utils/http"
	IMCIDL "git.xiaojukeji.com/falcon/imc-go-sdk/idl"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/jw_order/wanliu_order_strived_hook/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_strived_hook/framework"
	"git.xiaojukeji.com/falcon/zarya-async-api/models/rpc/imc"
	"git.xiaojukeji.com/falcon/zarya-async-api/models/rpc/unique"
)

const (
	jwTaxiGuidelineTerminalID = 300320 // JW打车终端ID

	// 乘客JW打车通知频次控制key
	PassengerJWOrderNotifyCountKey = "passenger_jw_order_notify_count"
)

type FeatureComponent_JWTaxiSmsNotify struct {
	framework.BaseComponent
	FeatureBO *IDL.BusinessInfo
}

func (c *FeatureComponent_JWTaxiSmsNotify) Run(ctx context.Context) error {
	// 检查订单领域模型是否已加载
	orderDM := c.BO().OrderDM()
	if orderDM == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "order domain model is nil")
		return nil
	}

	// 从订单领域模型获取终端ID
	terminalID := orderDM.PassengerTerminalId()
	if terminalID != jwTaxiGuidelineTerminalID {
		return nil
	}

	// 检查是否为国际订单（如果查不到就是国内订单）
	areaInfo, err := c.loadStartingAreaInfo(ctx, orderDM.NewLat(), orderDM.NewLng())
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "load starting area info error, err=%v", err)
		return nil
	}
	if areaInfo == nil {
		return nil
	}
	if areaInfo != nil && *areaInfo.CanonicalCountryCode != orderDM.CountryIsoCode() {
		log.Infof(ctx, "skip_international_order", "skip international order, location=%s, country=%s",
			*areaInfo.CanonicalCountryCode, orderDM.CountryIsoCode())
		return nil
	}

	// 检查频次控制 先去掉频次检查

	if !c.checkFrequencyControl(ctx, orderDM) {
		log.Infof(ctx, "frequency_control_limit", "frequency control limit reached for passenger_id=%d", orderDM.PassengerId())
		return nil
	}

	// 发送短信通知
	err = c.sendSmsToPassenger(ctx)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "send sms error, err=%v", err)
		return nil
	}
	return nil
}

// 检查频次控制，如果返回true表示可以发送，false表示不能发送
func (c *FeatureComponent_JWTaxiSmsNotify) checkFrequencyControl(ctx context.Context, orderDM *order.DomainModel) bool {
	// 直接在组件中实现频次控制逻辑
	canSend, err := c.checkPassengerJWOrderNotifyCount(ctx, orderDM)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "check frequency control error, err=%v", err)
		return false // 如果出错，不允许发送
	}

	return canSend
}

// 检查乘客JW打车通知频次控制
// 返回true表示可以发送通知，false表示不能发送通知
func (c *FeatureComponent_JWTaxiSmsNotify) checkPassengerJWOrderNotifyCount(ctx context.Context, orderDM *order.DomainModel) (bool, error) {
	if orderDM == nil {
		return false, fmt.Errorf("order domain model is nil")
	}

	// 从订单领域模型获取乘客ID
	passengerID := orderDM.PassengerId()

	// 检查订单ID是否有效
	if orderDM.OrderId() == nil {
		return false, fmt.Errorf("order id is nil")
	}

	// 从订单中获取城市ID
	cityID := orderDM.Area()

	// 如果城市ID无效，返回错误
	if cityID == 0 {
		return false, fmt.Errorf("invalid city id: %d", cityID)
	}
	// 创建请求
	req := unique.GetAndSetByAbilityReq{
		Id:                fmt.Sprintf("%d", passengerID),
		Keys:              []string{PassengerJWOrderNotifyCountKey},
		KeySuffixs:        []map[string]string{},
		CityId:            cityID, // 使用订单的城市ID
		IsNeedSetAfterGet: true,   // 本次消费
	}

	// 调用乘客领域的GetAndSetByAbility方法
	client := new(unique.ClientUnique)
	ability, err := client.GetAndSetByAbilityWithDomain(ctx, req, uniqueSDK.Domain_passenger)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "check passenger jw order notify count failed, err=%v", err)
		return false, err // 如果出错，不允许发送，并返回错误
	}

	// 格式化结果
	abilityMap := client.FormatCountAbilityMap(ctx, ability)

	// 检查结果
	return abilityMap[PassengerJWOrderNotifyCountKey].Flag, nil
}

func (c *FeatureComponent_JWTaxiSmsNotify) sendSmsToPassenger(ctx context.Context) error {
	orderDM := c.BO().OrderDM()
	var hintContent http.HintContent
	err := json.Unmarshal([]byte(orderDM.HintContent()), &hintContent)
	if err != nil {
		return err
	}

	lang := hintContent.Lang
	countryCode := orderDM.CountryCode()
	uid := biz.GetUidFromPid(orderDM.PassengerId())

	// 创建模板参数（本场景不需要额外参数）
	templateParams := map[string]string{}

	paramList := map[string]string{}
	// 发送短信通知
	return c.sendSmsNotification(ctx, uid, lang, countryCode, templateParams, paramList)
}

// 发送短信通知
func (c *FeatureComponent_JWTaxiSmsNotify) sendSmsNotification(ctx context.Context, uid int64, lang, countryCode string,
	templateParams map[string]string, paramList map[string]string) error {

	imcConfig := c.BO().GetModuleTypeConfig(ModuleName, "sms")

	if imcConfig == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "imc config is nil")
		return fmt.Errorf("imc config is nil")
	}

	req := &IMCIDL.TemplateMessageReq{
		Control: &IMCIDL.Control{},
		TmplMsg: &IMCIDL.TmplMessage{
			TemplateId: imcConfig.Key, // 短信模板ID
			Lang:       lang,
			Country:    countryCode,
			CombinedUsers: []*UniGWIDL.CombinedUser{{
				Uid: uid,
			}},
			TemplateParams: templateParams,
			FuncParams: &IMCIDL.FuncParams{
				LinkTracking: &IMCIDL.LinkTracking{
					ParamList: paramList,
				},
			},
			AppType: consts.AppTypeDomesticPassenger,
		},
	}
	config := imc.IMCConfigConfig{
		SecretKey: imcConfig.SecretKey,
		Bid:       imcConfig.Bid,
	}
	// 使用新的IMC配置发送消息
	_, err := imc.GetClientWithConfig(imc.IMCConfigWanliuOrderStrived).PushMessageV2WithConfig(ctx, req, imc.IMCConfigWanliuOrderStrived, config)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "send sms notification error, err=%v", err)
	}
	return err
}

// 加载订单的起点城市信息
func (c *FeatureComponent_JWTaxiSmsNotify) loadStartingAreaInfo(ctx context.Context, lat IDL2.Latitude, lng IDL2.Longitude) (*SDK.Cityinfo, error) {
	client := locsvr.GetClient(ctx)
	if client == nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "loc svr client init failed")
		return nil, errors.New("loc svr client init failed")
	}
	localHelper := biz.NewMapHelper(client)
	request := biz.GetAreaInfoByLocRequest{
		Lat:        lat,
		Lng:        lng,
		Token:      "D3A19B7FAA8BBE830970E671924A5EFA",
		WithCounty: true,
		MapType:    "wgs84",
	}
	locRes, err1 := localHelper.GetAreaInfoByLoc(ctx, &request)
	if errors.Is(err1, biz.ErrorCityInfoEmpty) {
		return nil, nil
	}
	if err1 != nil {
		return nil, err1
	}
	return locRes, nil
}
