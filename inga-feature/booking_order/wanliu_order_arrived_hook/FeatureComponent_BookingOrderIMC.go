package wanliu_order_arrived_hook

import (
	"context"
	"encoding/json"

	UniGWIDL "git.xiaojukeji.com/cc/uni-gateway-golang-sdk/v3/idl"

	"git.xiaojukeji.com/falcon/go-biz/utils/biz"
	"git.xiaojukeji.com/falcon/go-biz/utils/http"
	IMCIDL "git.xiaojukeji.com/falcon/imc-go-sdk/idl"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/consts"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"git.xiaojukeji.com/falcon/zarya-async-api/inga-feature/booking_order/wanliu_order_arrived_hook/IDL"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_arrived_hook/framework"
	"git.xiaojukeji.com/falcon/zarya-async-api/models/rpc/imc"
)

const (
	imcKey = "ibt_pax_booking_order_drv_arrived_notify_1733281153"
)

type FeatureComponent_BookingOrderIMC struct {
	framework.BaseComponent
	FeatureBO *IDL.BusinessInfo
}

func (c *FeatureComponent_BookingOrderIMC) Run(ctx context.Context) error {
	if !c.BO().OrderDM().Type().IsBookingOrder() {
		return nil
	}
	err := c.sendImcToPassenger(ctx)
	if err != nil {
		log.Warnf(ctx, consts.ErrnoIMCerror, "send imc error, err=%v", err)
		return nil
	}
	return nil
}

func (c *FeatureComponent_BookingOrderIMC) sendImcToPassenger(ctx context.Context) error {
	var hintCountent http.HintContent
	err := json.Unmarshal([]byte(c.BO().OrderDM().HintContent()), &hintCountent)
	if err != nil {
		return err
	}
	lang := hintCountent.Lang
	countryCode := hintCountent.LocationCountry
	uid := biz.GetUidFromPid(c.BO().OrderDM().PassengerId())

	req := &IMCIDL.TemplateMessageReq{
		Control: &IMCIDL.Control{},
		TmplMsg: &IMCIDL.TmplMessage{
			TemplateId: imcKey,
			Lang:       lang,
			Country:    countryCode,
			CombinedUsers: []*UniGWIDL.CombinedUser{{
				Uid: uid,
			}},
			TemplateParams: nil,
			FuncParams:     &IMCIDL.FuncParams{},
			AppType:        consts.AppTypePassenger,
		},
	}
	_, err = imc.GetClient().PushMessageV2(ctx, req)
	return err
}
