package IDL

import (
	"git.xiaojukeji.com/falcon/go-biz/domain_model/driver"
	"git.xiaojukeji.com/falcon/go-biz/domain_model/order"
	"git.xiaojukeji.com/falcon/go-biz/domain_model/vehicle"
	"git.xiaojukeji.com/falcon/zarya-async-api/logic/wanliu_order_strived_hook/IDL/base_object"
)

type BusinessInfo struct {
	base_object.BaseFeatureBusinessObject
	orderDM   *order.DomainModel
	driverDM  *driver.DomainModel
	vehicleDM *vehicle.DomainModel
}

// SetOrderDM 设置订单领域模型
func (b *BusinessInfo) SetOrderDM(dm *order.DomainModel) {
	b.orderDM = dm
}

// OrderDM 获取订单领域模型
func (b *BusinessInfo) OrderDM() *order.DomainModel {
	return b.orderDM
}

// SetDriverDM 设置司机领域模型
func (b *BusinessInfo) SetDriverDM(dm *driver.DomainModel) {
	b.driverDM = dm
}

// DriverDM 获取司机领域模型
func (b *BusinessInfo) DriverDM() *driver.DomainModel {
	return b.driverDM
}

// SetVehicleDM 设置车辆领域模型
func (b *BusinessInfo) SetVehicleDM(dm *vehicle.DomainModel) {
	b.vehicleDM = dm
}

// VehicleDM 获取车辆领域模型
func (b *BusinessInfo) VehicleDM() *vehicle.DomainModel {
	return b.vehicleDM
}
