name: booking_order # 预约单

# processor列表
# 一个feature 在一个 接口里的所有组件都放在一个 pakcage 里，被成为一个 processor
processor_list:

  - target_api: wanliu_order_reload_finished_hook
    target_project: **********************:falcon/zarya-async-api.git
    # target_api 和 target_project 标记要参与哪个模块哪个接口的逻辑
    # target_api  对应 zarya 框架中的接口标识

    processor_path: wanliu_order_reload_finished_hook # processor 对应 feature 项目的相对pakcage路径

    components: # 组件列表

      - component: FeatureComponent_BookingOrderIMC
        execution_position: dos_event  # 在接口组件 dos_event 后执行
        execution_priority: 1020 # 相同位置的组件里，执行的优先级。约小约先执行。
        is_break: false
