package delivery

// PlatformProcess d_delivery_platform_Process 表对应的结构体
type PlatformProcess struct {
	DeliveryID    int64 `db:"delivery_id" json:"delivery_id" fieldtag:"split_task_tag"`                                                           // 运单ID
	CityID        int64 `db:"city_id" json:"city_id"`                                                                                             // 城市ID
	Channel       int64 `db:"channel" json:"channel"`                                                                                             // 下单渠道，0: 线上正式单，1: 测试单
	Status        int64 `db:"status" json:"status" fieldtag:"orm_update_status,assign_action,split_task_tag,confirm_action"`                      // 当前状态（状态机状态码）
	SubStatus     int64 `db:"sub_status" json:"sub_status" fieldtag:"orm_update_status,assign_action,confirm_action"`                             // 子状态（）
	CurrRiderID   int64 `db:"curr_rider_id" json:"curr_rider_id" fieldtag:"orm_update_status,assign_action,confirm_action"`                       // 当前骑手
	PrevRiderID   int64 `db:"prev_rider_id" json:"prev_rider_id" fieldtag:"orm_update_status,assign_action,confirm_action"`                       // 上次分派骑手
	PrevStatus    int64 `db:"prev_status" json:"prev_status" fieldtag:"orm_update_status,assign_action,confirm_action"`                           // 上一个状态
	PrevSubStatus int64 `db:"prev_sub_status" json:"prev_sub_status" fieldtag:"orm_update_status,assign_action,confirm_action"`                   // 上一个字状态
	CreateTime    int64 `db:"create_time" json:"create_time"`                                                                                     // 记录创建时间
	UpdateTime    int64 `db:"update_time" json:"update_time" fieldtag:"orm_update_status,assign_action,orm_update,orm_update_eta,confirm_action"` // 记录更新时间
	DeliveryModel int64 `db:"delivery_model" json:"delivery_model"`                                                                               // 配送模式：平台配送, 混合配送, 自配送, 代购模式
	DConfirmTime  int64 `db:"d_confirm_time" json:"d_confirm_time" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"`          // D端确认接单时间
	AssignTime    int64 `db:"assign_time" json:"assign_time" fieldtag:"orm_update_status,assign_action,confirm_action"`                           // delivery服务处理分单时的时间戳
	DispatchTime  int64 `db:"dispatch_time" json:"dispatch_time" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"`            // 引擎的分单时间戳
	OrderID       int64 `db:"order_id" json:"order_id" fieldtag:"split_task_tag"`                                                                 // 订单ID

	//AbnormalReason      string  `db:"abnormal_reason" json:"abnormal_reason" fieldtag:"orm_update_status,confirm_action"`                                      // 异常原因
	//ReassignCount       int64   `db:"reassign_count" json:"reassign_count" fieldtag:"orm_update_status,confirm_action"`                                        // 改派次数           180， ReAssign 时+1
	//RescheduleCount     int64   `db:"reschedule_count" json:"reschedule_count" fieldtag:"orm_update_status,confirm_action"`                                    // 重新调度次数，  181，ReSchedule 时+1
	//PickupDelayDuration int64   `db:"pickup_delay_duration" json:"pickup_delay_duration" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"` // 出货延迟时间
	//SendDelayDuration   int64   `db:"send_delay_duration" json:"send_delay_duration" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"`     // 送货延迟时间
	//CurrTripID          int64   `db:"curr_trip_id" json:"curr_trip_id" fieldtag:"orm_update_status,assign_action,confirm_action"`                              // 当前Trip ID
	//PrevTripID          int64   `db:"prev_trip_id" json:"prev_trip_id" fieldtag:"orm_update_status,assign_action,confirm_action"`                              // 上次分派的TripID
	//PremiumPrice        int64   `db:"premium_price" json:"premium_price" fieldtag:"orm_update_status,assign_action,confirm_action"`                            // 运单改派溢价(单位: 滴分)
	//EstimatePrice       int64   `db:"estimate_price" json:"estimate_price" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"`               // 运单当前骑手预估价
	//DynamicPrice        int64   `db:"dynamic_price" json:"dynamic_price" fieldtag:"orm_update_status,assign_action,confirm_action"`                            // 动态价格
	//DispatchBuffer      int64   `db:"dispatch_buffer" json:"dispatch_buffer" fieldtag:"orm_update_status,assign_action,confirm_action"`                        // 派单buffer
	//EnvStream           int64   `db:"env_stream" json:"env_stream"`                                                                                            // redis的key相关，0:正式环境，1:预发布环境
	//RealEta             string  `db:"real_eta" json:"real_eta" fieldtag:"orm_update_eta" `                                                                     // 实时ETA
	//MealOutETA          int64   `db:"meal_out_eta" json:"meal_out_eta" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"`                   // 引擎计算的商家出货时间
	//DEta                int64   `db:"d_eta" json:"d_eta" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"`                                 // d 计算eta
	//BConfirmTime        int64   `db:"b_confirm_time" json:"b_confirm_time" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"`               // B端确认接单时间
	//DispatchFeatureInfo string  `db:"dispatch_feature_info" json:"dispatch_feature_info" fieldtag:"orm_update_status,assign_action,orm_update_feature_info"`   // D端位置特征信息
	//DispatchLat         float64 `db:"dispatch_lat" json:"dispatch_lat" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"`                   // 分单时骑手纬度
	//DispatchLng         float64 `db:"dispatch_lng" json:"dispatch_lng" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"`                   // 分单时骑手经度
	//EngineVersion       int64   `db:"engine_version" json:"engine_version" fieldtag:"orm_update_status,assign_action,orm_update,confirm_action"`               // 数据更新版本
	//RealDeliveryMethod  int64   `db:"real_delivery_method" json:"real_delivery_method" fieldtag:"orm_update_status,assign_action,confirm_action"`              // D端骑手实际交付方式
	//AdvanceMethod       int64   `db:"advance_method" json:"advance_method" fieldtag:"orm_update_status,assign_action,confirm_action"`                          // 现金单骑手垫付方式
	//BizLine             int64   `db:"biz_line" json:"biz_line"`                                                                                                // 业务线, 1:外卖, 2:闪送, 3:商超, 4:代购
	//DeliveryProcess     int64   `db:"delivery_process" json:"delivery_process"`                                                                                // 运单流程：先B后D，先D后B
	//ProcessID           int64   `db:"process_id" json:"process_id" fieldtag:"assign_action"`                                                                   // 分单流程ID,运单每次分骑手时生成，UUID
}
