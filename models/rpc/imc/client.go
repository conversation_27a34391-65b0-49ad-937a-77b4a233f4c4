package imc

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
	"net/http"

	IMC "git.xiaojukeji.com/falcon/imc-go-sdk"
	IMCIDL "git.xiaojukeji.com/falcon/imc-go-sdk/idl"
	legoTrace "git.xiaojukeji.com/lego/context-go"
)

// 配置信息已移至 config.go 文件

type Client struct {
}

// GetClient 获取默认的IMC客户端（使用旧的配置）
func GetClient() *Client {
	return &Client{}
}

// GetClientWithConfig 获取指定配置类型的IMC客户端
func GetClientWithConfig(configType IMCConfigType) *Client {
	// 这里只是返回客户端实例，实际的配置在调用方法时使用
	return &Client{}
}

// PushMessageV2 发送消息（使用旧的配置）
func (cli *Client) PushMessageV2(ctx context.Context, req *IMCIDL.TemplateMessageReq) (*IMCIDL.SendMessageRes, error) {
	return cli.PushMessageV2WithConfig(ctx, req, IMCConfigLegacy, IMCConfigConfig{})
}

// PushMessageV2WithConfig 使用指定配置发送消息
func (cli *Client) PushMessageV2WithConfig(ctx context.Context, req *IMCIDL.TemplateMessageReq, configType IMCConfigType, configSet IMCConfigConfig) (*IMCIDL.SendMessageRes, error) {
	if req.TmplMsg.Country == "" {
		req.TmplMsg.Country = "default"
	}

	// 根据配置类型获取对应的IMC配置
	appID, secretKey := GetIMCConfig(configType, configSet)
	client := IMC.New(appID, secretKey)
	// 每个trace只会发送一次
	sKey := fmt.Sprintf("%s_%s", legoTrace.GetTrace(ctx).GetTraceId(), req.TmplMsg.TemplateId)
	md5sum := md5.Sum([]byte(sKey))
	ctx = IMC.AddHeader(ctx, http.Header{"X-Idempotent-Key": []string{fmt.Sprintf("%x", md5sum)}})
	resp, err := client.SendIMCMessage(ctx, req)
	if err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return nil, errors.New(fmt.Sprintf("rquest imc err, errno=%d, errmsg=%s", resp.Code, resp.Message))
	}
	//增加发送日志
	reqJson, _ := json.Marshal(req)
	respJson, _ := json.Marshal(resp)
	log.Info("PushMessageV2WithConfig send message success", "traceId", legoTrace.GetTrace(ctx).GetTraceId(), "req", string(reqJson), "resp", string(respJson))
	return resp, nil
}
