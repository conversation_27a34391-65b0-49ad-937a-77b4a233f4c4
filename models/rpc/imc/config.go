package imc

import "git.xiaojukeji.com/falcon/go-biz/utils/converter"

// IMC配置类型
type IMCConfigType int

type IMCConfigConfig struct {
	Bid       string
	SecretKey string
}

const (
	// IMCConfigLegacy 旧的IMC配置
	IMCConfigLegacy IMCConfigType = iota
	// IMCConfigWanliuOrderStrived 新的IMC配置
	IMCConfigWanliuOrderStrived
)

// IMC配置信息
const (
	// 旧的IMC配置
	LegacyAppID     = 1901100112
	LegacySecretKey = "6BjCP0nq5fIXPx8!Xpkr0SVAUMdm3PQl"

	// 新的IMC配置
	NewAppID     = 328696724110751
	NewSecretKey = "c++3IlTxmgwqFwMgHWorAw=="
)

// GetIMCConfig 根据配置类型获取对应的IMC配置
func GetIMCConfig(configType IMCConfigType, configSet IMCConfigConfig) (appID int, secretKey string) {
	switch configType {
	case IMCConfigWanliuOrderStrived:
		if configSet.SecretKey == "" {
			configSet.SecretKey = NewSecretKey
		}
		//转化为int
		configSetBid := converter.StringToInt(configSet.Bid)
		return configSetBid, configSet.SecretKey
	default:
		return LegacyAppID, LegacySecretKey
	}
}
