package unique

const (
	DriverAutoAcceptIndexSettingsGuideKeyPrefix = "auto_accept_index_settings_guide_"
	DriverAutoAcceptIndexSettingsGuideKey       = "auto_accept_index_settings_guide_{product_id}"
	DriverAutoAcceptMissSerialPopupKeyPrefix    = "auto_accept_miss_serial_popup"
	DriverAutoAcceptMissSerialPopupKey          = "auto_accept_miss_serial_popup_{product_id}"

	DriverIndexTodoList = "driver_index_todo_list"

	// 乘客JW打车通知频次控制key
	PassengerJWOrderNotifyCountKey = "passenger_jw_order_notify_count"
)

type GetAndSetByAbilityReq struct {
	Id                string
	Keys              []string
	KeySuffixs        []map[string]string
	CityId            int32
	IsNeedSetAfterGet bool
}

type FormatCount struct {
	Flag                 bool `json:"flag"`
	TotalUsedCount       int  `json:"total_used_count"`
	TotalAvailableCount  int  `json:"total_available_count"`
	PeriodTotalUsedCount int  `json:"period_total_used_count"`
	PeriodAvailableCount int  `json:"period_available_count"`
}
