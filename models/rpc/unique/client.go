package unique

import (
	"context"
	"encoding/json"
	"fmt"

	uniqueSDK "git.xiaojukeji.com/dirpc/dirpc-go-http-Unique"
	"git.xiaojukeji.com/falcon/go-biz/sdk/unique"
	"git.xiaojukeji.com/falcon/go-biz/utils/assert"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/falcon/zarya-async-api/common/handlers/log"
)

type ClientUnique struct {
}

var cClient *unique.Client

func Init() {
	var err error
	cClient, err = unique.NewClient()
	if err != nil {
		panic(fmt.Sprintf("init unique err, err=%v", err))
	}
}

func GetAndSetByAbilityV2(ctx context.Context, req *GetAndSetByAbilityReq) (map[string]string, error) {
	if len(req.Keys) == 0 {
		return nil, nil
	}
	client := new(ClientUnique)
	return client.GetAndSetByAbility(ctx, *req)
}

func (c *ClientUnique) GetAndSetByAbility(ctx context.Context, req GetAndSetByAbilityReq) (map[string]string, error) {
	return c.GetAndSetByAbilityWithDomain(ctx, req, uniqueSDK.Domain_driver)
}

func (c *ClientUnique) GetAndSetByAbilityWithDomain(ctx context.Context, req GetAndSetByAbilityReq, domain uniqueSDK.Domain) (map[string]string, error) {
	featureGetByAbilityReq := &uniqueSDK.FeatureGetByAbilityReq{
		Domain:     domain,
		Id:         req.Id,
		Keys:       req.Keys,
		KeySuffixs: req.KeySuffixs,
		OptionParam: map[string]string{
			"city_id":               converter.Int32ToString(req.CityId),
			"is_need_set_after_get": converter.BoolToString(req.IsNeedSetAfterGet),
		},
	}
	ability, err := cClient.GetAndSetByAbility(ctx, featureGetByAbilityReq)
	if assert.IsNotNil(err) {
		return nil, err
	}

	return ability, nil
}

func (c *ClientUnique) FormatCountAbilityMap(ctx context.Context, ability map[string]string) map[string]FormatCount {
	abilityMap := make(map[string]FormatCount, len(ability))
	for key, value := range ability {
		formatCount := FormatCount{}
		if err := json.Unmarshal([]byte(value), &formatCount); err != nil {
			log.Warnf(ctx, 70024, "unique_unmarshal_failed||data=%s||err=%v", value, err)
			continue
		}
		abilityMap[key] = formatCount
	}
	return abilityMap
}

func GetByAbility(ctx context.Context, req *uniqueSDK.FeatureGetByAbilityReq) (map[string]FormatCount, error) {
	if len(req.Keys) == 0 {
		return nil, nil
	}
	result, err := cClient.GetByAbility(ctx, req)
	if err != nil {
		return nil, err
	}
	clientI := new(ClientUnique)
	resp := clientI.FormatCountAbilityMap(ctx, result)
	return resp, nil
}
