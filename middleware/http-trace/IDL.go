package trace

import (
	"net/http"

	"git.xiaojukeji.com/falcon/go-biz/IDL"
	"git.xiaojukeji.com/falcon/go-biz/utils/assert"
	"git.xiaojukeji.com/falcon/go-biz/utils/converter"
	"git.xiaojukeji.com/nuwa/binding"
)

// PrivateParams 接口所需的私参
type PrivateParams struct {
}

// 直接解析http参数的结构体
type rawRequest struct {
	BffCommon     IDL.CommonParams `json:"common"`
	BffLogin      IDL.LoginParams  `json:"login"`
	PrivateParams PrivateParams    `json:"request"`
}

// ApiRequest 要在后续业务总线(service)中传递的对象，充血模型，包含各种Request级别的逻辑
// 示例见下 IsB2bChannel, FromName, SetUid
type ApiRequest struct {
	//利用私有变量，保护起来request,避免request里的数据直接暴露,从而保证所有的数据都是通过getter获取。
	//所有的参数特殊逻辑只需要做一次，并且对外隐藏了参数的细节，如果端上有bug，传入不同类型的数据，错误的数据，都可以在这一层兼容。
	//这样保证的是，整个Request起到了类似DTO转换层的作用。
	request *rawRequest
}

// NewApiRequest 构造ApiRequest,不要自己直接初始化 ApiRequest,很多内部属性依赖构造函数
func NewApiRequest(req *http.Request) (*ApiRequest, error) {
	raw := rawRequest{}
	if err := binding.JSON.Bind(req, &raw); err != nil {
		return nil, err
	}
	return &ApiRequest{request: &raw}, nil
}

func (r ApiRequest) Lat() IDL.Latitude        { return r.request.BffCommon.Lat }
func (r ApiRequest) Lng() IDL.Longitude       { return r.request.BffCommon.Lng }
func (r ApiRequest) LocationCountry() *string { return r.request.BffCommon.LocationCountry }

// Lang 空指针保护，类似语法糖，避免调用方费劲,使用converter的指针函数，避免isNil判断
func (r *ApiRequest) Lang() string {
	return converter.PtrCopyString(r.request.BffCommon.Lang)
}

func (r ApiRequest) UtcOffset() int32 {
	return converter.PtrCopyInt32(r.request.BffCommon.UtcOffset)
}

func (r *ApiRequest) DriverId() int64 {
	return r.request.BffLogin.DriverId
}

func (r *ApiRequest) MapType() IDL.MapType {
	if assert.IsNotNil(r.request.BffCommon.MapType) {
		return *r.request.BffCommon.MapType
	}
	return IDL.MapTypeWgs84
}

func (r *ApiRequest) Location() IDL.Point {
	return *IDL.NewPoint(r.request.BffCommon.Lat, r.request.BffCommon.Lng)
}

func (r *ApiRequest) ProductId() int32 {
	return *r.request.BffCommon.ProductId
}

func (r *ApiRequest) CityId() int32 {
	return converter.PtrCopyInt32(r.request.BffCommon.LocationCityId)
}

func (r *ApiRequest) TerminalId() IDL.TerminalId {
	return r.request.BffCommon.TerminalId
}

func (r *ApiRequest) AppVersion() IDL.VersionString {
	return r.request.BffCommon.AppVersion
}

func (r *ApiRequest) Ticket() string {
	return r.request.BffCommon.Ticket
}

func (r *ApiRequest) Country() string {
	return converter.PtrCopyString(r.request.BffCommon.LocationCountry)
}

func (r *ApiRequest) Phone() string {
	return r.request.BffLogin.Phone
}
